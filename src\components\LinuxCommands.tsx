import React, { useState, useEffect } from 'react';
import { Terminal, Search, Book, Code, Shield, Server, Network, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CommandData {
  command: string;
  description: string;
  syntax: string;
  example: string;
  output: string;
  category: string;
  difficulty: 'Basic' | 'Intermediate' | 'Advanced';
}

const linuxCommands: CommandData[] = [
  // File System Commands
  {
    command: 'ls',
    description: 'List directory contents',
    syntax: 'ls [options] [directory]',
    example: 'ls -la /home/<USER>',
    output: `total 24
drwxr-xr-x 3 <USER> <GROUP> 4096 Dec 16 10:30 .
drwxr-xr-x 3 <USER> <GROUP> 4096 Dec 15 09:20 ..
-rw-r--r-- 1 <USER> <GROUP>  220 Dec 15 09:20 .bash_logout
-rw-r--r-- 1 <USER> <GROUP> 3771 Dec 15 09:20 .bashrc
drwxr-xr-x 2 <USER> <GROUP> 4096 Dec 16 10:30 Documents`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'cd',
    description: 'Change directory',
    syntax: 'cd [directory]',
    example: 'cd /var/log',
    output: `user@linux:/var/log$`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'pwd',
    description: 'Print working directory',
    syntax: 'pwd',
    example: 'pwd',
    output: `/home/<USER>/Documents`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'mkdir',
    description: 'Create directories',
    syntax: 'mkdir [options] directory',
    example: 'mkdir -p /tmp/test/nested',
    output: `Directory created successfully`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'rmdir',
    description: 'Remove empty directories',
    syntax: 'rmdir [options] directory',
    example: 'rmdir /tmp/empty_folder',
    output: `Directory removed successfully`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'rm',
    description: 'Remove files and directories',
    syntax: 'rm [options] file/directory',
    example: 'rm -rf /tmp/test',
    output: `Files and directories removed`,
    category: 'File System',
    difficulty: 'Intermediate'
  },
  {
    command: 'cp',
    description: 'Copy files and directories',
    syntax: 'cp [options] source destination',
    example: 'cp -r /home/<USER>/docs /backup/',
    output: `Files copied successfully`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'mv',
    description: 'Move/rename files and directories',
    syntax: 'mv source destination',
    example: 'mv oldfile.txt newfile.txt',
    output: `File moved/renamed successfully`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'find',
    description: 'Search for files and directories',
    syntax: 'find [path] [expression]',
    example: 'find /home -name "*.txt" -type f',
    output: `/home/<USER>/document.txt
/home/<USER>/notes.txt
/home/<USER>/readme.txt`,
    category: 'File System',
    difficulty: 'Intermediate'
  },
  {
    command: 'locate',
    description: 'Find files using database',
    syntax: 'locate [pattern]',
    example: 'locate passwd',
    output: `/etc/passwd
/usr/bin/passwd
/usr/share/man/man1/passwd.1.gz`,
    category: 'File System',
    difficulty: 'Basic'
  },

  // File Content Commands
  {
    command: 'cat',
    description: 'Display file contents',
    syntax: 'cat [options] file',
    example: 'cat /etc/passwd',
    output: `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin`,
    category: 'File Content',
    difficulty: 'Basic'
  },
  {
    command: 'less',
    description: 'View file contents page by page',
    syntax: 'less [options] file',
    example: 'less /var/log/syslog',
    output: `Dec 16 10:30:01 linux systemd[1]: Started Session 1 of user root.
Dec 16 10:30:02 linux kernel: [12345.678901] USB disconnect
Dec 16 10:30:03 linux NetworkManager[1234]: <info> device (eth0): state change
:`,
    category: 'File Content',
    difficulty: 'Basic'
  },
  {
    command: 'head',
    description: 'Display first lines of file',
    syntax: 'head [options] file',
    example: 'head -n 5 /etc/passwd',
    output: `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
sync:x:4:65534:sync:/bin:/bin/sync`,
    category: 'File Content',
    difficulty: 'Basic'
  },
  {
    command: 'tail',
    description: 'Display last lines of file',
    syntax: 'tail [options] file',
    example: 'tail -f /var/log/syslog',
    output: `Dec 16 10:35:01 linux systemd[1]: Started Daily apt download activities.
Dec 16 10:35:02 linux systemd[1]: apt-daily.service: Succeeded.
Dec 16 10:35:03 linux CRON[12345]: (root) CMD (command -v debian-sa1 > /dev/null)`,
    category: 'File Content',
    difficulty: 'Basic'
  },
  {
    command: 'grep',
    description: 'Search text patterns in files',
    syntax: 'grep [options] pattern file',
    example: 'grep -r "error" /var/log/',
    output: `/var/log/syslog:Dec 16 10:30:01 linux kernel: error: device not found
/var/log/auth.log:Dec 16 10:31:15 linux sshd[1234]: error: authentication failed
/var/log/apache2/error.log:[error] [client *************] File does not exist`,
    category: 'File Content',
    difficulty: 'Intermediate'
  },

  // System Information
  {
    command: 'ps',
    description: 'Display running processes',
    syntax: 'ps [options]',
    example: 'ps aux',
    output: `USER       PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root         1  0.0  0.1 225316  9876 ?        Ss   10:30   0:01 /sbin/init
root         2  0.0  0.0      0     0 ?        S    10:30   0:00 [kthreadd]
root      1234  0.1  0.5  65432 12345 ?        S    10:31   0:00 /usr/sbin/sshd`,
    category: 'System Info',
    difficulty: 'Intermediate'
  },
  {
    command: 'top',
    description: 'Display and update running processes',
    syntax: 'top [options]',
    example: 'top',
    output: `Tasks: 234 total,   1 running, 233 sleeping,   0 stopped,   0 zombie
%Cpu(s):  2.3 us,  1.2 sy,  0.0 ni, 96.1 id,  0.4 wa,  0.0 hi,  0.0 si,  0.0 st
MiB Mem :   7982.1 total,   1234.5 free,   2345.6 used,   4402.0 buff/cache
MiB Swap:   2048.0 total,   2048.0 free,      0.0 used.   5123.4 avail Mem`,
    category: 'System Info',
    difficulty: 'Intermediate'
  },
  {
    command: 'htop',
    description: 'Interactive process viewer',
    syntax: 'htop',
    example: 'htop',
    output: `  1  [|||||||||||||||||||||||||||||||||||||||||||||||||| 52.3%]
  2  [||||||||||||||||||||||||||||||||||||||||||||||     45.1%]
  3  [|||||||||||||||||||||||||||||||||||||||||||||||||| 48.7%]
  4  [||||||||||||||||||||||||||||||||||||||||||         38.2%]
  Mem[||||||||||||||||||||||||||||||||||||||||     3.21G/7.98G]
  Swp[                                             0K/2.00G]`,
    category: 'System Info',
    difficulty: 'Intermediate'
  },

  // Network Commands
  {
    command: 'ping',
    description: 'Send ICMP echo requests to network hosts',
    syntax: 'ping [options] destination',
    example: 'ping -c 4 google.com',
    output: `PING google.com (**************) 56(84) bytes of data.
64 bytes from lga25s62-in-f14.1e100.net (**************): icmp_seq=1 ttl=118 time=12.3 ms
64 bytes from lga25s62-in-f14.1e100.net (**************): icmp_seq=2 ttl=118 time=11.8 ms
64 bytes from lga25s62-in-f14.1e100.net (**************): icmp_seq=3 ttl=118 time=12.1 ms
64 bytes from lga25s62-in-f14.1e100.net (**************): icmp_seq=4 ttl=118 time=11.9 ms

--- google.com ping statistics ---
4 packets transmitted, 4 received, 0% packet loss, time 3005ms
rtt min/avg/max/mdev = 11.834/12.025/12.321/0.203 ms`,
    category: 'Network',
    difficulty: 'Basic'
  },
  {
    command: 'wget',
    description: 'Download files from web servers',
    syntax: 'wget [options] URL',
    example: 'wget https://example.com/file.zip',
    output: `--2023-12-16 10:30:15--  https://example.com/file.zip
Resolving example.com (example.com)... *************
Connecting to example.com (example.com)|*************|:443... connected.
HTTP request sent, awaiting response... 200 OK
Length: 1048576 (1.0M) [application/zip]
Saving to: 'file.zip'

file.zip            100%[===================>]   1.00M  2.50MB/s    in 0.4s

2023-12-16 10:30:16 (2.50 MB/s) - 'file.zip' saved [1048576/1048576]`,
    category: 'Network',
    difficulty: 'Basic'
  },
  {
    command: 'curl',
    description: 'Transfer data from or to servers',
    syntax: 'curl [options] URL',
    example: 'curl -I https://httpbin.org/get',
    output: `HTTP/2 200
date: Sat, 16 Dec 2023 15:30:15 GMT
content-type: application/json
content-length: 314
server: gunicorn/19.9.0
access-control-allow-origin: *
access-control-allow-credentials: true`,
    category: 'Network',
    difficulty: 'Intermediate'
  },
  {
    command: 'netstat',
    description: 'Display network connections and routing tables',
    syntax: 'netstat [options]',
    example: 'netstat -tuln',
    output: `Active Internet connections (only servers)
Proto Recv-Q Send-Q Local Address           Foreign Address         State
tcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN
tcp        0      0 127.0.0.1:631           0.0.0.0:*               LISTEN
tcp6       0      0 :::22                   :::*                    LISTEN
udp        0      0 0.0.0.0:68              0.0.0.0:*
udp        0      0 0.0.0.0:631             0.0.0.0:*`,
    category: 'Network',
    difficulty: 'Intermediate'
  },
  {
    command: 'ss',
    description: 'Display socket statistics',
    syntax: 'ss [options]',
    example: 'ss -tuln',
    output: `Netid  State      Recv-Q Send-Q Local Address:Port               Peer Address:Port
udp    UNCONN     0      0      0.0.0.0:68                  0.0.0.0:*
udp    UNCONN     0      0      0.0.0.0:631                 0.0.0.0:*
tcp    LISTEN     0      128    0.0.0.0:22                  0.0.0.0:*
tcp    LISTEN     0      5      127.0.0.1:631               0.0.0.0:*
tcp    LISTEN     0      128       [::]:22                     [::]:*`,
    category: 'Network',
    difficulty: 'Intermediate'
  },

  // Security Commands
  {
    command: 'sudo',
    description: 'Execute commands as another user',
    syntax: 'sudo [options] command',
    example: 'sudo apt update',
    output: `[sudo] password for user:
Hit:1 http://archive.ubuntu.com/ubuntu focal InRelease
Get:2 http://archive.ubuntu.com/ubuntu focal-updates InRelease [114 kB]
Get:3 http://archive.ubuntu.com/ubuntu focal-backports InRelease [108 kB]
Get:4 http://security.ubuntu.com/ubuntu focal-security InRelease [114 kB]
Fetched 336 kB in 2s (168 kB/s)
Reading package lists... Done`,
    category: 'Security',
    difficulty: 'Basic'
  },
  {
    command: 'chmod',
    description: 'Change file permissions',
    syntax: 'chmod [options] mode file',
    example: 'chmod 755 script.sh',
    output: `Permissions changed successfully`,
    category: 'Security',
    difficulty: 'Intermediate'
  },
  {
    command: 'chown',
    description: 'Change file ownership',
    syntax: 'chown [options] owner:group file',
    example: 'chown user:group file.txt',
    output: `Ownership changed successfully`,
    category: 'Security',
    difficulty: 'Intermediate'
  },
  {
    command: 'passwd',
    description: 'Change user password',
    syntax: 'passwd [username]',
    example: 'passwd user',
    output: `Changing password for user.
Current password:
New password:
Retype new password:
passwd: password updated successfully`,
    category: 'Security',
    difficulty: 'Basic'
  },

  // Text Processing Commands
  {
    command: 'awk',
    description: 'Pattern scanning and processing language',
    syntax: 'awk \'pattern { action }\' file',
    example: 'awk \'{print $1, $3}\' /etc/passwd',
    output: `root: 0:
daemon: 1:
bin: 2:
sys: 3:
sync: 4:`,
    category: 'Text Processing',
    difficulty: 'Advanced'
  },
  {
    command: 'sed',
    description: 'Stream editor for filtering and transforming text',
    syntax: 'sed [options] \'command\' file',
    example: 'sed \'s/old/new/g\' file.txt',
    output: `Text with old replaced by new
Another line with new text
Final line with new content`,
    category: 'Text Processing',
    difficulty: 'Intermediate'
  },
  {
    command: 'sort',
    description: 'Sort lines of text files',
    syntax: 'sort [options] file',
    example: 'sort -n numbers.txt',
    output: `1
5
10
25
100`,
    category: 'Text Processing',
    difficulty: 'Basic'
  },
  {
    command: 'uniq',
    description: 'Report or omit repeated lines',
    syntax: 'uniq [options] file',
    example: 'uniq -c sorted_file.txt',
    output: `      3 apple
      1 banana
      2 cherry
      1 date`,
    category: 'Text Processing',
    difficulty: 'Basic'
  },
  {
    command: 'cut',
    description: 'Remove sections from each line of files',
    syntax: 'cut [options] file',
    example: 'cut -d: -f1,3 /etc/passwd',
    output: `root:0
daemon:1
bin:2
sys:3
sync:4`,
    category: 'Text Processing',
    difficulty: 'Intermediate'
  },

  // System Administration
  {
    command: 'systemctl',
    description: 'Control systemd services',
    syntax: 'systemctl [command] [service]',
    example: 'systemctl status ssh',
    output: `● ssh.service - OpenBSD Secure Shell server
   Loaded: loaded (/lib/systemd/system/ssh.service; enabled; vendor preset: enabled)
   Active: active (running) since Sat 2023-12-16 10:30:15 UTC; 2h 15min ago
     Docs: man:sshd(8)
           man:sshd_config(5)
  Process: 1234 ExecStartPre=/usr/sbin/sshd -t (code=exited, status=0/SUCCESS)
 Main PID: 1235 (sshd)
    Tasks: 1 (limit: 4915)
   Memory: 2.8M
   CGroup: /system.slice/ssh.service
           └─1235 /usr/sbin/sshd -D`,
    category: 'System Admin',
    difficulty: 'Intermediate'
  },
  {
    command: 'crontab',
    description: 'Schedule tasks to run automatically',
    syntax: 'crontab [options]',
    example: 'crontab -l',
    output: `# Edit this file to introduce tasks to be run by cron.
#
# m h  dom mon dow   command
0 2 * * * /usr/bin/backup.sh
30 6 * * 1 /usr/bin/weekly_report.sh
0 0 1 * * /usr/bin/monthly_cleanup.sh`,
    category: 'System Admin',
    difficulty: 'Intermediate'
  },
  {
    command: 'df',
    description: 'Display filesystem disk space usage',
    syntax: 'df [options] [filesystem]',
    example: 'df -h',
    output: `Filesystem      Size  Used Avail Use% Mounted on
/dev/sda1        20G  8.5G   11G  45% /
/dev/sda2       100G   45G   50G  48% /home
tmpfs           2.0G     0  2.0G   0% /dev/shm
/dev/sdb1       500G  250G  225G  53% /var/backups`,
    category: 'System Admin',
    difficulty: 'Basic'
  },
  {
    command: 'du',
    description: 'Display directory space usage',
    syntax: 'du [options] [directory]',
    example: 'du -sh /var/log/*',
    output: `4.0K	/var/log/alternatives.log
12K	/var/log/apt
8.0K	/var/log/auth.log
4.0K	/var/log/bootstrap.log
16K	/var/log/dpkg.log
2.1M	/var/log/journal
4.0K	/var/log/kern.log`,
    category: 'System Admin',
    difficulty: 'Basic'
  },
  {
    command: 'mount',
    description: 'Mount filesystems',
    syntax: 'mount [options] device mountpoint',
    example: 'mount /dev/sdb1 /mnt/usb',
    output: `Filesystem mounted successfully`,
    category: 'System Admin',
    difficulty: 'Intermediate'
  }
];

// Auto-typing hook for terminal simulation
const useTypewriter = (text: string, speed: number = 50) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, speed]);

  return displayText;
};

const LinuxCommands = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCommand, setSelectedCommand] = useState<CommandData | null>(null);
  const [isTyping, setIsTyping] = useState(false);

  const categories = ['All', ...Array.from(new Set(linuxCommands.map(cmd => cmd.category)))];

  const filteredCommands = linuxCommands.filter(cmd => {
    const matchesCategory = selectedCategory === 'All' || cmd.category === selectedCategory;
    const matchesSearch = cmd.command.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cmd.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'File System': return <Server className="h-4 w-4" />;
      case 'File Content': return <Book className="h-4 w-4" />;
      case 'System Info': return <Shield className="h-4 w-4" />;
      case 'Network': return <Network className="h-4 w-4" />;
      case 'Security': return <Lock className="h-4 w-4" />;
      default: return <Code className="h-4 w-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Basic': return 'text-green-400 border-green-400/50 bg-green-400/10';
      case 'Intermediate': return 'text-yellow-400 border-yellow-400/50 bg-yellow-400/10';
      case 'Advanced': return 'text-red-400 border-red-400/50 bg-red-400/10';
      default: return 'text-gray-400 border-gray-400/50 bg-gray-400/10';
    }
  };

  return (
    <section className="py-16 px-6 md:px-12 bg-gradient-to-b from-psyco-black-DEFAULT to-psyco-black-card">
      <div className="max-w-7xl mx-auto">
        {/* Terminal Header */}
        <div className="mb-8 terminal-glow">
          <div className="bg-gray-900 border border-psyco-green-muted/30 rounded-none p-3 font-mono text-sm">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-3 h-3 bg-red-500 rounded-none"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-none"></div>
                <div className="w-3 h-3 bg-green-500 rounded-none"></div>
              </div>
              <div className="text-psyco-green-light flex items-center space-x-1">
                <Terminal className="h-4 w-4" />
                <span>Wolf@root:~</span>
                <span className="text-white">Linux Commands Reference --help</span>
              </div>
            </div>
          </div>
          <div className="bg-black border-x border-b border-psyco-green-muted/30 rounded-none p-6 font-mono terminal-scan">
            <div className="text-psyco-green-light">
              <span className="text-gray-400">$</span> man linux-commands
            </div>
            <div className="mt-2 text-gray-300">
              <span className="text-psyco-green-DEFAULT">[INFO]</span> Loading Linux command database...
            </div>
            <div className="text-gray-300">
              <span className="text-psyco-green-DEFAULT">[SUCCESS]</span> Found {linuxCommands.length} commands
            </div>
            <div className="text-gray-300">
              <span className="text-blue-400">[READY]</span> Terminal ready for command execution
            </div>
          </div>
        </div>

        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Terminal className="h-8 w-8 text-psyco-green-DEFAULT" />
            <h2 className="text-4xl font-bold text-white">Linux Commands Reference</h2>
          </div>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Comprehensive guide to Linux terminal commands with examples and outputs. 
            Master the command line for cybersecurity and system administration.
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search commands..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-900 border border-psyco-green-muted/30 rounded-none text-white placeholder-gray-400 focus:outline-none focus:border-psyco-green-DEFAULT font-mono"
            />
          </div>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={cn(
                  "px-4 py-2 rounded-none border font-mono text-sm transition-colors duration-300",
                  selectedCategory === category
                    ? "bg-psyco-green-DEFAULT text-white border-psyco-green-DEFAULT"
                    : "bg-gray-900 text-gray-300 border-gray-700 hover:border-psyco-green-muted"
                )}
              >
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(category)}
                  <span>{category}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Commands Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {filteredCommands.map((cmd, index) => (
            <div
              key={cmd.command}
              className="glassmorphism p-6 rounded-none border-2 border-psyco-green-muted/30 card-hover animate-fade-in cursor-pointer"
              style={{ animationDelay: `${index * 100}ms` }}
              onClick={() => setSelectedCommand(cmd)}
            >
              {/* Command Header */}
              <div className="bg-gray-900 -m-6 mb-4 p-3 font-mono text-xs border-b border-psyco-green-muted/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-red-500 rounded-none"></div>
                      <div className="w-2 h-2 bg-yellow-500 rounded-none"></div>
                      <div className="w-2 h-2 bg-green-500 rounded-none"></div>
                    </div>
                    <span className="text-psyco-green-light">{cmd.command}.sh</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getCategoryIcon(cmd.category)}
                    <span className="text-gray-400">{cmd.category}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-white font-mono mb-2">{cmd.command}</h3>
                  <p className="text-gray-300 text-sm mb-3">{cmd.description}</p>
                </div>
                <span className={cn(
                  "px-2 py-1 rounded-none border text-xs font-mono",
                  getDifficultyColor(cmd.difficulty)
                )}>
                  {cmd.difficulty}
                </span>
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium text-white mb-2 font-mono">Syntax:</h4>
                <div className="bg-black p-2 rounded-none border border-gray-700 font-mono text-xs">
                  <span className="text-psyco-green-light">{cmd.syntax}</span>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium text-white mb-2 font-mono">Example:</h4>
                <div className="bg-black p-2 rounded-none border border-gray-700 font-mono text-xs">
                  <span className="text-gray-400">$ </span>
                  <span className="text-yellow-400">{cmd.example}</span>
                </div>
              </div>

              <div className="text-center">
                <button className="bg-psyco-green-DEFAULT hover:bg-psyco-green-light text-white px-4 py-2 rounded-none transition-colors duration-300 font-mono text-sm">
                  Execute Command
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Terminal Simulation Modal */}
        {selectedCommand && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-900 border-2 border-psyco-green-muted/50 rounded-none max-w-4xl w-full max-h-[80vh] overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-gray-800 p-3 font-mono text-sm border-b border-psyco-green-muted/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-3 h-3 bg-red-500 rounded-none"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-none"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-none"></div>
                    </div>
                    <Terminal className="h-4 w-4 text-psyco-green-light" />
                    <span className="text-psyco-green-light">Wolf@root:~ - {selectedCommand.command}</span>
                  </div>
                  <button
                    onClick={() => setSelectedCommand(null)}
                    className="text-red-400 hover:text-red-300 font-bold"
                  >
                    ✕
                  </button>
                </div>
              </div>

              {/* Terminal Content */}
              <div className="bg-black p-6 font-mono text-sm overflow-y-auto max-h-[60vh]">
                <div className="mb-4">
                  <div className="text-psyco-green-light mb-2">
                    <span className="text-gray-400">$ </span>
                    <span className="text-yellow-400">{selectedCommand.example}</span>
                  </div>
                  <div className="text-gray-300 whitespace-pre-line">
                    {selectedCommand.output}
                  </div>
                </div>

                <div className="border-t border-gray-700 pt-4 mt-4">
                  <h4 className="text-white mb-2">Command Information:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-psyco-green-light">Command:</span>
                      <span className="text-white ml-2">{selectedCommand.command}</span>
                    </div>
                    <div>
                      <span className="text-psyco-green-light">Category:</span>
                      <span className="text-white ml-2">{selectedCommand.category}</span>
                    </div>
                    <div>
                      <span className="text-psyco-green-light">Difficulty:</span>
                      <span className={cn("ml-2",
                        selectedCommand.difficulty === 'Basic' ? 'text-green-400' :
                        selectedCommand.difficulty === 'Intermediate' ? 'text-yellow-400' : 'text-red-400'
                      )}>
                        {selectedCommand.difficulty}
                      </span>
                    </div>
                    <div>
                      <span className="text-psyco-green-light">Syntax:</span>
                      <span className="text-white ml-2">{selectedCommand.syntax}</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <span className="text-psyco-green-light">Description:</span>
                    <p className="text-gray-300 mt-1">{selectedCommand.description}</p>
                  </div>
                </div>

                <div className="mt-4 text-psyco-green-light">
                  <span className="text-gray-400">$ </span>
                  <span className="terminal-cursor">|</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* No Results */}
        {filteredCommands.length === 0 && (
          <div className="text-center py-16">
            <Terminal className="h-16 w-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl text-white mb-2 font-mono">No commands found</h3>
            <p className="text-gray-400 font-mono">Try adjusting your search or category filter</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default LinuxCommands;
