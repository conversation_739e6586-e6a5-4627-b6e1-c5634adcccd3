import React, { useState, useEffect } from 'react';
import { Terminal, Search, Book, Code, Shield, Server, Network, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CommandData {
  command: string;
  description: string;
  syntax: string;
  example: string;
  output: string;
  category: string;
  difficulty: 'Basic' | 'Intermediate' | 'Advanced';
}

const linuxCommands: CommandData[] = [
  // File System Commands
  {
    command: 'ls',
    description: 'List directory contents',
    syntax: 'ls [options] [directory]',
    example: 'ls -la /home/<USER>',
    output: `total 24
drwxr-xr-x 3 <USER> <GROUP> 4096 Dec 16 10:30 .
drwxr-xr-x 3 <USER> <GROUP> 4096 Dec 15 09:20 ..
-rw-r--r-- 1 <USER> <GROUP>  220 Dec 15 09:20 .bash_logout
-rw-r--r-- 1 <USER> <GROUP> 3771 Dec 15 09:20 .bashrc
drwxr-xr-x 2 <USER> <GROUP> 4096 Dec 16 10:30 Documents`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'cd',
    description: 'Change directory',
    syntax: 'cd [directory]',
    example: 'cd /var/log',
    output: `user@linux:/var/log$`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'pwd',
    description: 'Print working directory',
    syntax: 'pwd',
    example: 'pwd',
    output: `/home/<USER>/Documents`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'mkdir',
    description: 'Create directories',
    syntax: 'mkdir [options] directory',
    example: 'mkdir -p /tmp/test/nested',
    output: `Directory created successfully`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'rmdir',
    description: 'Remove empty directories',
    syntax: 'rmdir [options] directory',
    example: 'rmdir /tmp/empty_folder',
    output: `Directory removed successfully`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'rm',
    description: 'Remove files and directories',
    syntax: 'rm [options] file/directory',
    example: 'rm -rf /tmp/test',
    output: `Files and directories removed`,
    category: 'File System',
    difficulty: 'Intermediate'
  },
  {
    command: 'cp',
    description: 'Copy files and directories',
    syntax: 'cp [options] source destination',
    example: 'cp -r /home/<USER>/docs /backup/',
    output: `Files copied successfully`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'mv',
    description: 'Move/rename files and directories',
    syntax: 'mv source destination',
    example: 'mv oldfile.txt newfile.txt',
    output: `File moved/renamed successfully`,
    category: 'File System',
    difficulty: 'Basic'
  },
  {
    command: 'find',
    description: 'Search for files and directories',
    syntax: 'find [path] [expression]',
    example: 'find /home -name "*.txt" -type f',
    output: `/home/<USER>/document.txt
/home/<USER>/notes.txt
/home/<USER>/readme.txt`,
    category: 'File System',
    difficulty: 'Intermediate'
  },
  {
    command: 'locate',
    description: 'Find files using database',
    syntax: 'locate [pattern]',
    example: 'locate passwd',
    output: `/etc/passwd
/usr/bin/passwd
/usr/share/man/man1/passwd.1.gz`,
    category: 'File System',
    difficulty: 'Basic'
  },

  // File Content Commands
  {
    command: 'cat',
    description: 'Display file contents',
    syntax: 'cat [options] file',
    example: 'cat /etc/passwd',
    output: `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin`,
    category: 'File Content',
    difficulty: 'Basic'
  },
  {
    command: 'less',
    description: 'View file contents page by page',
    syntax: 'less [options] file',
    example: 'less /var/log/syslog',
    output: `Dec 16 10:30:01 linux systemd[1]: Started Session 1 of user root.
Dec 16 10:30:02 linux kernel: [12345.678901] USB disconnect
Dec 16 10:30:03 linux NetworkManager[1234]: <info> device (eth0): state change
:`,
    category: 'File Content',
    difficulty: 'Basic'
  },
  {
    command: 'head',
    description: 'Display first lines of file',
    syntax: 'head [options] file',
    example: 'head -n 5 /etc/passwd',
    output: `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
sync:x:4:65534:sync:/bin:/bin/sync`,
    category: 'File Content',
    difficulty: 'Basic'
  },
  {
    command: 'tail',
    description: 'Display last lines of file',
    syntax: 'tail [options] file',
    example: 'tail -f /var/log/syslog',
    output: `Dec 16 10:35:01 linux systemd[1]: Started Daily apt download activities.
Dec 16 10:35:02 linux systemd[1]: apt-daily.service: Succeeded.
Dec 16 10:35:03 linux CRON[12345]: (root) CMD (command -v debian-sa1 > /dev/null)`,
    category: 'File Content',
    difficulty: 'Basic'
  },
  {
    command: 'grep',
    description: 'Search text patterns in files',
    syntax: 'grep [options] pattern file',
    example: 'grep -r "error" /var/log/',
    output: `/var/log/syslog:Dec 16 10:30:01 linux kernel: error: device not found
/var/log/auth.log:Dec 16 10:31:15 linux sshd[1234]: error: authentication failed
/var/log/apache2/error.log:[error] [client *************] File does not exist`,
    category: 'File Content',
    difficulty: 'Intermediate'
  },

  // System Information
  {
    command: 'ps',
    description: 'Display running processes',
    syntax: 'ps [options]',
    example: 'ps aux',
    output: `USER       PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root         1  0.0  0.1 225316  9876 ?        Ss   10:30   0:01 /sbin/init
root         2  0.0  0.0      0     0 ?        S    10:30   0:00 [kthreadd]
root      1234  0.1  0.5  65432 12345 ?        S    10:31   0:00 /usr/sbin/sshd`,
    category: 'System Info',
    difficulty: 'Intermediate'
  },
  {
    command: 'top',
    description: 'Display and update running processes',
    syntax: 'top [options]',
    example: 'top',
    output: `Tasks: 234 total,   1 running, 233 sleeping,   0 stopped,   0 zombie
%Cpu(s):  2.3 us,  1.2 sy,  0.0 ni, 96.1 id,  0.4 wa,  0.0 hi,  0.0 si,  0.0 st
MiB Mem :   7982.1 total,   1234.5 free,   2345.6 used,   4402.0 buff/cache
MiB Swap:   2048.0 total,   2048.0 free,      0.0 used.   5123.4 avail Mem`,
    category: 'System Info',
    difficulty: 'Intermediate'
  },
  {
    command: 'htop',
    description: 'Interactive process viewer',
    syntax: 'htop',
    example: 'htop',
    output: `  1  [|||||||||||||||||||||||||||||||||||||||||||||||||| 52.3%]
  2  [||||||||||||||||||||||||||||||||||||||||||||||     45.1%]
  3  [|||||||||||||||||||||||||||||||||||||||||||||||||| 48.7%]
  4  [||||||||||||||||||||||||||||||||||||||||||         38.2%]
  Mem[||||||||||||||||||||||||||||||||||||||||     3.21G/7.98G]
  Swp[                                             0K/2.00G]`,
    category: 'System Info',
    difficulty: 'Intermediate'
  },

  // Network Commands
  {
    command: 'ping',
    description: 'Send ICMP echo requests to network hosts',
    syntax: 'ping [options] destination',
    example: 'ping -c 4 google.com',
    output: `PING google.com (**************) 56(84) bytes of data.
64 bytes from lga25s62-in-f14.1e100.net (**************): icmp_seq=1 ttl=118 time=12.3 ms
64 bytes from lga25s62-in-f14.1e100.net (**************): icmp_seq=2 ttl=118 time=11.8 ms
64 bytes from lga25s62-in-f14.1e100.net (**************): icmp_seq=3 ttl=118 time=12.1 ms
64 bytes from lga25s62-in-f14.1e100.net (**************): icmp_seq=4 ttl=118 time=11.9 ms

--- google.com ping statistics ---
4 packets transmitted, 4 received, 0% packet loss, time 3005ms
rtt min/avg/max/mdev = 11.834/12.025/12.321/0.203 ms`,
    category: 'Network',
    difficulty: 'Basic'
  },
  {
    command: 'wget',
    description: 'Download files from web servers',
    syntax: 'wget [options] URL',
    example: 'wget https://example.com/file.zip',
    output: `--2023-12-16 10:30:15--  https://example.com/file.zip
Resolving example.com (example.com)... *************
Connecting to example.com (example.com)|*************|:443... connected.
HTTP request sent, awaiting response... 200 OK
Length: 1048576 (1.0M) [application/zip]
Saving to: 'file.zip'

file.zip            100%[===================>]   1.00M  2.50MB/s    in 0.4s

2023-12-16 10:30:16 (2.50 MB/s) - 'file.zip' saved [1048576/1048576]`,
    category: 'Network',
    difficulty: 'Basic'
  },
  {
    command: 'curl',
    description: 'Transfer data from or to servers',
    syntax: 'curl [options] URL',
    example: 'curl -I https://httpbin.org/get',
    output: `HTTP/2 200
date: Sat, 16 Dec 2023 15:30:15 GMT
content-type: application/json
content-length: 314
server: gunicorn/19.9.0
access-control-allow-origin: *
access-control-allow-credentials: true`,
    category: 'Network',
    difficulty: 'Intermediate'
  },
  {
    command: 'netstat',
    description: 'Display network connections and routing tables',
    syntax: 'netstat [options]',
    example: 'netstat -tuln',
    output: `Active Internet connections (only servers)
Proto Recv-Q Send-Q Local Address           Foreign Address         State
tcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN
tcp        0      0 127.0.0.1:631           0.0.0.0:*               LISTEN
tcp6       0      0 :::22                   :::*                    LISTEN
udp        0      0 0.0.0.0:68              0.0.0.0:*
udp        0      0 0.0.0.0:631             0.0.0.0:*`,
    category: 'Network',
    difficulty: 'Intermediate'
  },
  {
    command: 'ss',
    description: 'Display socket statistics',
    syntax: 'ss [options]',
    example: 'ss -tuln',
    output: `Netid  State      Recv-Q Send-Q Local Address:Port               Peer Address:Port
udp    UNCONN     0      0      0.0.0.0:68                  0.0.0.0:*
udp    UNCONN     0      0      0.0.0.0:631                 0.0.0.0:*
tcp    LISTEN     0      128    0.0.0.0:22                  0.0.0.0:*
tcp    LISTEN     0      5      127.0.0.1:631               0.0.0.0:*
tcp    LISTEN     0      128       [::]:22                     [::]:*`,
    category: 'Network',
    difficulty: 'Intermediate'
  },

  // Security Commands
  {
    command: 'sudo',
    description: 'Execute commands as another user',
    syntax: 'sudo [options] command',
    example: 'sudo apt update',
    output: `[sudo] password for user:
Hit:1 http://archive.ubuntu.com/ubuntu focal InRelease
Get:2 http://archive.ubuntu.com/ubuntu focal-updates InRelease [114 kB]
Get:3 http://archive.ubuntu.com/ubuntu focal-backports InRelease [108 kB]
Get:4 http://security.ubuntu.com/ubuntu focal-security InRelease [114 kB]
Fetched 336 kB in 2s (168 kB/s)
Reading package lists... Done`,
    category: 'Security',
    difficulty: 'Basic'
  },
  {
    command: 'chmod',
    description: 'Change file permissions',
    syntax: 'chmod [options] mode file',
    example: 'chmod 755 script.sh',
    output: `Permissions changed successfully`,
    category: 'Security',
    difficulty: 'Intermediate'
  },
  {
    command: 'chown',
    description: 'Change file ownership',
    syntax: 'chown [options] owner:group file',
    example: 'chown user:group file.txt',
    output: `Ownership changed successfully`,
    category: 'Security',
    difficulty: 'Intermediate'
  },
  {
    command: 'passwd',
    description: 'Change user password',
    syntax: 'passwd [username]',
    example: 'passwd user',
    output: `Changing password for user.
Current password:
New password:
Retype new password:
passwd: password updated successfully`,
    category: 'Security',
    difficulty: 'Basic'
  },

  // Text Processing Commands
  {
    command: 'awk',
    description: 'Pattern scanning and processing language',
    syntax: 'awk \'pattern { action }\' file',
    example: 'awk \'{print $1, $3}\' /etc/passwd',
    output: `root: 0:
daemon: 1:
bin: 2:
sys: 3:
sync: 4:`,
    category: 'Text Processing',
    difficulty: 'Advanced'
  },
  {
    command: 'sed',
    description: 'Stream editor for filtering and transforming text',
    syntax: 'sed [options] \'command\' file',
    example: 'sed \'s/old/new/g\' file.txt',
    output: `Text with old replaced by new
Another line with new text
Final line with new content`,
    category: 'Text Processing',
    difficulty: 'Intermediate'
  },
  {
    command: 'sort',
    description: 'Sort lines of text files',
    syntax: 'sort [options] file',
    example: 'sort -n numbers.txt',
    output: `1
5
10
25
100`,
    category: 'Text Processing',
    difficulty: 'Basic'
  },
  {
    command: 'uniq',
    description: 'Report or omit repeated lines',
    syntax: 'uniq [options] file',
    example: 'uniq -c sorted_file.txt',
    output: `      3 apple
      1 banana
      2 cherry
      1 date`,
    category: 'Text Processing',
    difficulty: 'Basic'
  },
  {
    command: 'cut',
    description: 'Remove sections from each line of files',
    syntax: 'cut [options] file',
    example: 'cut -d: -f1,3 /etc/passwd',
    output: `root:0
daemon:1
bin:2
sys:3
sync:4`,
    category: 'Text Processing',
    difficulty: 'Intermediate'
  },

  // System Administration
  {
    command: 'systemctl',
    description: 'Control systemd services',
    syntax: 'systemctl [command] [service]',
    example: 'systemctl status ssh',
    output: `● ssh.service - OpenBSD Secure Shell server
   Loaded: loaded (/lib/systemd/system/ssh.service; enabled; vendor preset: enabled)
   Active: active (running) since Sat 2023-12-16 10:30:15 UTC; 2h 15min ago
     Docs: man:sshd(8)
           man:sshd_config(5)
  Process: 1234 ExecStartPre=/usr/sbin/sshd -t (code=exited, status=0/SUCCESS)
 Main PID: 1235 (sshd)
    Tasks: 1 (limit: 4915)
   Memory: 2.8M
   CGroup: /system.slice/ssh.service
           └─1235 /usr/sbin/sshd -D`,
    category: 'System Admin',
    difficulty: 'Intermediate'
  },
  {
    command: 'crontab',
    description: 'Schedule tasks to run automatically',
    syntax: 'crontab [options]',
    example: 'crontab -l',
    output: `# Edit this file to introduce tasks to be run by cron.
#
# m h  dom mon dow   command
0 2 * * * /usr/bin/backup.sh
30 6 * * 1 /usr/bin/weekly_report.sh
0 0 1 * * /usr/bin/monthly_cleanup.sh`,
    category: 'System Admin',
    difficulty: 'Intermediate'
  },
  {
    command: 'df',
    description: 'Display filesystem disk space usage',
    syntax: 'df [options] [filesystem]',
    example: 'df -h',
    output: `Filesystem      Size  Used Avail Use% Mounted on
/dev/sda1        20G  8.5G   11G  45% /
/dev/sda2       100G   45G   50G  48% /home
tmpfs           2.0G     0  2.0G   0% /dev/shm
/dev/sdb1       500G  250G  225G  53% /var/backups`,
    category: 'System Admin',
    difficulty: 'Basic'
  },
  {
    command: 'du',
    description: 'Display directory space usage',
    syntax: 'du [options] [directory]',
    example: 'du -sh /var/log/*',
    output: `4.0K	/var/log/alternatives.log
12K	/var/log/apt
8.0K	/var/log/auth.log
4.0K	/var/log/bootstrap.log
16K	/var/log/dpkg.log
2.1M	/var/log/journal
4.0K	/var/log/kern.log`,
    category: 'System Admin',
    difficulty: 'Basic'
  },
  {
    command: 'mount',
    description: 'Mount filesystems',
    syntax: 'mount [options] device mountpoint',
    example: 'mount /dev/sdb1 /mnt/usb',
    output: `Filesystem mounted successfully`,
    category: 'System Admin',
    difficulty: 'Intermediate'
  }
];

// Enhanced auto-typing hook for terminal simulation
const useTypewriter = (text: string, speed: number = 50, startDelay: number = 0) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isStarted, setIsStarted] = useState(false);

  useEffect(() => {
    if (!isStarted) {
      const startTimeout = setTimeout(() => {
        setIsStarted(true);
      }, startDelay);
      return () => clearTimeout(startTimeout);
    }
  }, [startDelay, isStarted]);

  useEffect(() => {
    if (isStarted && currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, speed, isStarted]);

  const reset = () => {
    setDisplayText('');
    setCurrentIndex(0);
    setIsStarted(false);
  };

  return { displayText, isComplete: currentIndex >= text.length, reset };
};

const LinuxCommands = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCommand, setSelectedCommand] = useState<CommandData | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [showOutput, setShowOutput] = useState(false);

  const categories = ['All', ...Array.from(new Set(linuxCommands.map(cmd => cmd.category)))];

  const filteredCommands = linuxCommands.filter(cmd => {
    const matchesCategory = selectedCategory === 'All' || cmd.category === selectedCategory;
    const matchesSearch = cmd.command.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cmd.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'File System': return <Server className="h-4 w-4" />;
      case 'File Content': return <Book className="h-4 w-4" />;
      case 'System Info': return <Shield className="h-4 w-4" />;
      case 'Network': return <Network className="h-4 w-4" />;
      case 'Security': return <Lock className="h-4 w-4" />;
      default: return <Code className="h-4 w-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Basic': return 'text-green-400 border-green-400/50 bg-green-400/10';
      case 'Intermediate': return 'text-yellow-400 border-yellow-400/50 bg-yellow-400/10';
      case 'Advanced': return 'text-red-400 border-red-400/50 bg-red-400/10';
      default: return 'text-gray-400 border-gray-400/50 bg-gray-400/10';
    }
  };

  return (
    <section className="py-16 px-6 md:px-12 bg-gradient-to-b from-psyco-black-DEFAULT to-psyco-black-card">
      <div className="max-w-7xl mx-auto">
        {/* Modern Terminal Header */}
        <div className="mb-12 terminal-glow">
          <div className="bg-gradient-to-r from-gray-900 to-gray-800 border border-psyco-green-muted/40 rounded-t-2xl p-4 font-mono text-sm shadow-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex space-x-2">
                  <div className="w-4 h-4 bg-red-500 rounded-full shadow-lg hover:bg-red-400 transition-colors cursor-pointer"></div>
                  <div className="w-4 h-4 bg-yellow-500 rounded-full shadow-lg hover:bg-yellow-400 transition-colors cursor-pointer"></div>
                  <div className="w-4 h-4 bg-green-500 rounded-full shadow-lg hover:bg-green-400 transition-colors cursor-pointer"></div>
                </div>
                <div className="text-psyco-green-light flex items-center space-x-2">
                  <Terminal className="h-5 w-5" />
                  <span className="font-semibold">Wolf@root:~</span>
                  <span className="text-white">Linux Commands Reference --help</span>
                </div>
              </div>
              <div className="text-xs text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
                {linuxCommands.length} commands loaded
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-b from-black to-gray-900 border-x border-b border-psyco-green-muted/40 rounded-b-2xl p-8 font-mono terminal-scan shadow-2xl">
            <div className="space-y-3">
              <div className="text-psyco-green-light flex items-center">
                <span className="text-gray-400 mr-2">$</span>
                <span className="bg-gray-800 px-2 py-1 rounded">man linux-commands</span>
              </div>
              <div className="ml-4 space-y-2">
                <div className="text-gray-300 flex items-center">
                  <span className="text-psyco-green-DEFAULT bg-psyco-green-DEFAULT/20 px-2 py-1 rounded mr-2">[INFO]</span>
                  Loading Linux command database...
                </div>
                <div className="text-gray-300 flex items-center">
                  <span className="text-psyco-green-DEFAULT bg-psyco-green-DEFAULT/20 px-2 py-1 rounded mr-2">[SUCCESS]</span>
                  Found {linuxCommands.length} commands
                </div>
                <div className="text-gray-300 flex items-center">
                  <span className="text-blue-400 bg-blue-400/20 px-2 py-1 rounded mr-2">[READY]</span>
                  Terminal ready for command execution
                </div>
                <div className="text-gray-300 flex items-center">
                  <span className="text-purple-400 bg-purple-400/20 px-2 py-1 rounded mr-2">[SYSTEM]</span>
                  Interactive mode enabled
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Terminal className="h-8 w-8 text-psyco-green-DEFAULT" />
            <h2 className="text-4xl font-bold text-white">Linux Commands Reference</h2>
          </div>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Comprehensive guide to Linux terminal commands with examples and outputs. 
            Master the command line for cybersecurity and system administration.
          </p>
        </div>

        {/* Enhanced Search and Filter */}
        <div className="mb-12 space-y-6">
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search commands, descriptions, or examples..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-6 py-4 bg-gradient-to-r from-gray-900 to-gray-800 border border-psyco-green-muted/40 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-psyco-green-DEFAULT focus:ring-2 focus:ring-psyco-green-DEFAULT/20 font-mono text-sm shadow-lg transition-all duration-300"
              />
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-400 font-mono">
              <span>Found:</span>
              <span className="bg-psyco-green-DEFAULT/20 text-psyco-green-light px-3 py-1 rounded-full font-semibold">
                {filteredCommands.length}
              </span>
              <span>commands</span>
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={cn(
                  "px-4 py-3 rounded-xl border font-mono text-sm transition-all duration-300 shadow-lg hover:scale-105",
                  selectedCategory === category
                    ? "bg-gradient-to-r from-psyco-green-DEFAULT to-psyco-green-light text-white border-psyco-green-DEFAULT shadow-psyco-green-DEFAULT/50"
                    : "bg-gradient-to-r from-gray-900 to-gray-800 text-gray-300 border-gray-700 hover:border-psyco-green-muted hover:text-white"
                )}
              >
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(category)}
                  <span className="font-semibold">{category}</span>
                  {selectedCategory === category && (
                    <span className="bg-white/20 text-xs px-2 py-1 rounded-full">
                      {linuxCommands.filter(cmd => category === 'All' || cmd.category === category).length}
                    </span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Modern Commands Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {filteredCommands.map((cmd, index) => (
            <div
              key={cmd.command}
              className="glassmorphism p-0 rounded-2xl border-2 border-psyco-green-muted/40 card-hover animate-fade-in cursor-pointer overflow-hidden shadow-2xl hover:shadow-psyco-green-DEFAULT/20 transition-all duration-500 hover:scale-105"
              style={{ animationDelay: `${index * 100}ms` }}
              onClick={() => setSelectedCommand(cmd)}
            >
              {/* Modern Command Header */}
              <div className="bg-gradient-to-r from-gray-900 to-gray-800 p-4 font-mono text-sm border-b border-psyco-green-muted/40">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full shadow-md"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full shadow-md"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full shadow-md"></div>
                    </div>
                    <span className="text-psyco-green-light font-semibold">{cmd.command}.sh</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-gray-800 px-3 py-1 rounded-full">
                    {getCategoryIcon(cmd.category)}
                    <span className="text-gray-300 text-xs">{cmd.category}</span>
                  </div>
                </div>
              </div>

              {/* Command Content */}
              <div className="p-6 bg-gradient-to-b from-gray-900/50 to-black/50">

                <div className="flex items-start justify-between mb-6">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-white font-mono mb-3 flex items-center">
                      <span className="text-psyco-green-DEFAULT mr-2">$</span>
                      {cmd.command}
                    </h3>
                    <p className="text-gray-300 text-sm leading-relaxed">{cmd.description}</p>
                  </div>
                  <span className={cn(
                    "px-3 py-2 rounded-full border text-xs font-mono font-semibold shadow-lg",
                    getDifficultyColor(cmd.difficulty)
                  )}>
                    {cmd.difficulty}
                  </span>
                </div>

                <div className="space-y-4 mb-6">
                  <div>
                    <h4 className="text-sm font-semibold text-psyco-green-light mb-3 font-mono flex items-center">
                      <Code className="h-4 w-4 mr-2" />
                      Syntax:
                    </h4>
                    <div className="bg-gradient-to-r from-black to-gray-900 p-4 rounded-xl border border-gray-700 font-mono text-sm shadow-inner">
                      <span className="text-psyco-green-light">{cmd.syntax}</span>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-semibold text-psyco-green-light mb-3 font-mono flex items-center">
                      <Terminal className="h-4 w-4 mr-2" />
                      Example:
                    </h4>
                    <div className="bg-gradient-to-r from-black to-gray-900 p-4 rounded-xl border border-gray-700 font-mono text-sm shadow-inner">
                      <div className="flex items-center">
                        <span className="text-gray-400 mr-2">$</span>
                        <span className="text-yellow-400">{cmd.example}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <button className="bg-gradient-to-r from-psyco-green-DEFAULT to-psyco-green-light hover:from-psyco-green-light hover:to-psyco-green-DEFAULT text-white px-6 py-3 rounded-xl transition-all duration-300 font-mono text-sm font-semibold shadow-lg hover:shadow-psyco-green-DEFAULT/50 transform hover:scale-105">
                    <div className="flex items-center space-x-2">
                      <Terminal className="h-4 w-4" />
                      <span>Execute Command</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Terminal Simulation Modal */}
        {selectedCommand && (
          <TerminalModal
            command={selectedCommand}
            onClose={() => {
              setSelectedCommand(null);
              setShowOutput(false);
            }}
          />
        )}

        {/* No Results */}
        {filteredCommands.length === 0 && (
          <div className="text-center py-20">
            <div className="bg-gradient-to-b from-gray-900 to-black p-12 rounded-2xl border border-gray-700 max-w-md mx-auto">
              <Terminal className="h-20 w-20 text-gray-600 mx-auto mb-6" />
              <h3 className="text-2xl text-white mb-4 font-mono font-bold">No commands found</h3>
              <p className="text-gray-400 font-mono leading-relaxed">
                Try adjusting your search terms or select a different category to explore more commands.
              </p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('All');
                }}
                className="mt-6 bg-psyco-green-DEFAULT hover:bg-psyco-green-light text-white px-6 py-3 rounded-xl transition-all duration-300 font-mono text-sm font-semibold"
              >
                Reset Filters
              </button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

// Enhanced Terminal Modal Component
const TerminalModal = ({ command, onClose }: { command: CommandData; onClose: () => void }) => {
  const [showCommand, setShowCommand] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const commandTyping = useTypewriter(command.example, 80, 500);
  const outputTyping = useTypewriter(command.output, 30, 1500);

  useEffect(() => {
    setShowCommand(true);
    const timer = setTimeout(() => {
      setShowOutput(true);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fade-in">
      <div className="bg-gradient-to-b from-gray-900 to-black border-2 border-psyco-green-muted/60 rounded-2xl max-w-5xl w-full max-h-[85vh] overflow-hidden shadow-2xl">
        {/* Modern Terminal Header */}
        <div className="bg-gradient-to-r from-gray-800 to-gray-700 p-4 font-mono text-sm border-b border-psyco-green-muted/40">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex space-x-2">
                <div className="w-4 h-4 bg-red-500 rounded-full shadow-lg hover:bg-red-400 transition-colors cursor-pointer" onClick={onClose}></div>
                <div className="w-4 h-4 bg-yellow-500 rounded-full shadow-lg hover:bg-yellow-400 transition-colors cursor-pointer"></div>
                <div className="w-4 h-4 bg-green-500 rounded-full shadow-lg hover:bg-green-400 transition-colors cursor-pointer"></div>
              </div>
              <div className="flex items-center space-x-2">
                <Terminal className="h-5 w-5 text-psyco-green-light" />
                <span className="text-psyco-green-light font-semibold">Wolf@root:~</span>
                <span className="text-white">- {command.command}</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-xs text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
                {command.category} • {command.difficulty}
              </div>
              <button
                onClick={onClose}
                className="text-red-400 hover:text-red-300 font-bold text-lg hover:scale-110 transition-all duration-200"
              >
                ✕
              </button>
            </div>
          </div>
        </div>

        {/* Enhanced Terminal Content */}
        <div className="bg-gradient-to-b from-black to-gray-900 p-8 font-mono text-sm overflow-y-auto max-h-[70vh] terminal-scan">
          <div className="space-y-6">
            {/* Command Execution */}
            <div className="bg-gray-900/50 p-6 rounded-xl border border-gray-700">
              <div className="text-psyco-green-light mb-4 flex items-center">
                <span className="text-gray-400 mr-2">$</span>
                <span className="text-yellow-400">
                  {showCommand ? commandTyping.displayText : ''}
                  {showCommand && !commandTyping.isComplete && <span className="terminal-cursor">|</span>}
                </span>
              </div>

              {showOutput && (
                <div className="text-gray-300 whitespace-pre-line leading-relaxed bg-black/50 p-4 rounded-lg border border-gray-800">
                  {outputTyping.displayText}
                  {!outputTyping.isComplete && <span className="terminal-cursor">|</span>}
                </div>
              )}
            </div>

            {/* Command Information Panel */}
            <div className="bg-gray-900/50 p-6 rounded-xl border border-gray-700">
              <h4 className="text-psyco-green-light mb-4 font-semibold flex items-center">
                <Shield className="h-4 w-4 mr-2" />
                Command Information:
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <span className="text-psyco-green-light font-semibold w-20">Command:</span>
                    <span className="text-white bg-gray-800 px-2 py-1 rounded font-mono">{command.command}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-psyco-green-light font-semibold w-20">Category:</span>
                    <span className="text-white bg-gray-800 px-2 py-1 rounded">{command.category}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <span className="text-psyco-green-light font-semibold w-20">Difficulty:</span>
                    <span className={cn("px-2 py-1 rounded font-semibold",
                      command.difficulty === 'Basic' ? 'text-green-400 bg-green-400/20' :
                      command.difficulty === 'Intermediate' ? 'text-yellow-400 bg-yellow-400/20' : 'text-red-400 bg-red-400/20'
                    )}>
                      {command.difficulty}
                    </span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-psyco-green-light font-semibold w-20 mt-1">Syntax:</span>
                    <span className="text-gray-300 bg-gray-800 px-2 py-1 rounded font-mono text-xs flex-1">{command.syntax}</span>
                  </div>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-700">
                <span className="text-psyco-green-light font-semibold">Description:</span>
                <p className="text-gray-300 mt-2 leading-relaxed">{command.description}</p>
              </div>
            </div>

            {/* Interactive Prompt */}
            <div className="bg-gray-900/50 p-4 rounded-xl border border-gray-700">
              <div className="text-psyco-green-light flex items-center">
                <span className="text-gray-400 mr-2">$</span>
                <span className="terminal-cursor">|</span>
                <span className="ml-2 text-gray-500 text-xs">Ready for next command...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinuxCommands;

export default LinuxCommands;
