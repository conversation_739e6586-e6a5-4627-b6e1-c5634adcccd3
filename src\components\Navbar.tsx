
import React, { useState, useEffect, useRef } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Menu,
  X,
  Home,
  Settings,
  BookOpen,
  Star,
  Map,
  Calendar,
  Terminal,
  User,
  ChevronDown,
  Phone,
  Mail
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useResponsive } from '@/hooks/use-mobile';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();
  const navRef = useRef<HTMLDivElement>(null);
  const { isMobile, isTablet, isTouchDevice, breakpoint } = useResponsive();

  // Handle scroll effects
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setScrolled(scrollPosition > 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    setIsOpen(false);
  }, [location.pathname]);

  // Handle click outside to close mobile menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Enhanced navigation links with icons
  const navLinks = [
    { name: 'Home', path: '/', icon: Home },
    { name: 'Join Course', path: '/services', icon: Settings },
    { name: 'Course Roadmap', path: '/roadmap', icon: Map },
    { name: 'Events', path: '/events', icon: Calendar },
    { name: 'Linux Commands', path: '/linux-commands', icon: Terminal },
    { name: 'Author', path: '/author', icon: User },
    { name: 'Blog', path: '/blog', icon: BookOpen },
    { name: 'References', path: '/references', icon: Star },
  ];

  const contactInfo = [
    { icon: Phone, text: '+91 63743 44424', href: 'tel:+916374344424' },
    { icon: Mail, text: '<EMAIL>', href: '<EMAIL>' },
  ];

  return (
    <nav
      ref={navRef}
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
        scrolled 
          ? 'glassmorphism bg-black/90 backdrop-blur-xl py-2 shadow-lg' 
          : 'bg-transparent py-3',
        'px-4 sm:px-6 lg:px-8'
      )}
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <NavLink to="/" className="flex items-center flex-shrink-0">
            <div className={cn(
              'transition-all duration-300',
              scrolled ? 'h-12 w-auto' : 'h-14 w-auto sm:h-16'
            )}>
              <img 
                src="/images/logo.png" 
                alt="PSK Services Logo" 
                className="h-full w-auto object-contain"
                loading="eager"
              />
            </div>
          </NavLink>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navLinks.map((link) => {
              const IconComponent = link.icon;
              return (
                <NavLink
                  key={link.path}
                  to={link.path}
                  className={({ isActive }) =>
                    cn(
                      'flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300',
                      'text-sm font-medium tracking-wide relative group nav-link-enhanced',
                      'hover:bg-green-500/10 hover:text-green-400 focus-enhanced',
                      isTouchDevice && 'touch-target',
                      isActive 
                        ? 'text-green-400 bg-green-500/10' 
                        : 'text-gray-200'
                    )
                  }
                >
                  <IconComponent size={isTablet ? 14 : 16} />
                  <span className={cn(
                    'font-display',
                    isTablet ? 'text-xs' : 'text-sm'
                  )}>{link.name}</span>
                  <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-green-400 transition-all duration-300 group-hover:w-full" />
                </NavLink>
              );
            })}
          </div>

          {/* Desktop Contact Info */}
          <div className="hidden xl:flex items-center space-x-4">
            {contactInfo.map((contact, index) => {
              const IconComponent = contact.icon;
              return (
                <a
                  key={index}
                  href={contact.href}
                  className="flex items-center space-x-2 text-xs text-gray-300 hover:text-green-400 transition-colors duration-300"
                >
                  <IconComponent size={14} />
                  <span>{contact.text}</span>
                </a>
              );
            })}
          </div>

          {/* Mobile menu button */}
          <button
            className={cn(
              'lg:hidden p-3 rounded-lg',
              'text-white hover:text-green-400 hover:bg-green-500/10',
              'focus:outline-none focus:ring-2 focus:ring-green-500/50',
              'touch-target focus-enhanced smooth-transition btn-touch no-zoom'
            )}
            onClick={() => setIsOpen(!isOpen)}
            aria-label="Toggle navigation menu"
            aria-expanded={isOpen}
          >
            {isOpen ? <X size={isMobile ? 22 : 20} /> : <Menu size={isMobile ? 22 : 20} />}
          </button>
        </div>

        {/* Mobile Navigation Menu */}
        <div
          className={cn(
            'lg:hidden fixed inset-x-0 top-16 z-40 transition-all duration-300 ease-in-out',
            'scroll-smooth-touch',
            isOpen 
              ? 'opacity-100 translate-y-0 pointer-events-auto' 
              : 'opacity-0 -translate-y-4 pointer-events-none'
          )}
        >
          <div className="glassmorphism-navbar mx-4 rounded-xl shadow-2xl border border-green-500/20">
            <div className="px-6 py-4 space-y-2 max-h-[80vh] overflow-y-auto scroll-smooth-touch">
              {navLinks.map((link, index) => {
                const IconComponent = link.icon;
                return (
                  <NavLink
                    key={link.path}
                    to={link.path}
                    className={({ isActive }) =>
                      cn(
                        'flex items-center space-x-3 px-4 py-4 rounded-lg',
                        'font-medium group touch-target focus-enhanced smooth-transition no-zoom',
                        'hover:bg-green-500/10 hover:text-green-400 hover:translate-x-1',
                        'mobile-menu-item',
                        isMobile ? 'text-base nav-text-mobile' : 'text-sm',
                        isActive 
                          ? 'text-green-400 bg-green-500/10' 
                          : 'text-gray-200'
                      )
                    }
                    style={{ animationDelay: `${(index + 1) * 0.05}s` }}
                  >
                    <IconComponent size={isMobile ? 20 : 18} />
                    <span className="font-display no-select">{link.name}</span>
                    <ChevronDown 
                      size={12} 
                      className="ml-auto opacity-50 group-hover:opacity-100 transition-opacity" 
                    />
                  </NavLink>
                );
              })}
              
              {/* Mobile Contact Section */}
              <div className="pt-4 mt-4 border-t border-green-500/20">
                <h3 className={cn(
                  'font-semibold text-green-400 mb-3 px-4 font-display',
                  isMobile ? 'text-sm' : 'text-xs'
                )}>Contact Us</h3>
                <div className="space-y-2">
                  {contactInfo.map((contact, index) => {
                    const IconComponent = contact.icon;
                    return (
                      <a
                        key={index}
                        href={contact.href}
                        className={cn(
                          'flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-green-400',
                          'transition-colors duration-300 touch-target focus-enhanced rounded-lg',
                          isMobile ? 'text-sm' : 'text-xs'
                        )}
                      >
                        <IconComponent size={isMobile ? 18 : 16} />
                        <span className="font-body">{contact.text}</span>
                      </a>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu backdrop */}
      {isOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-30"
          onClick={() => setIsOpen(false)}
        />
      )}
    </nav>
  );
};

export default Navbar;
