import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, Shield, Terminal, Trophy, Code, Network, Server } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { cn } from '@/lib/utils';
import speaker1 from '@/assets/speaker1.png';
import speaker2 from '@/assets/speaker2.png';

interface EventProps {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  coordinator: {
    name: string;
    image: string;
  };
  type: 'ctf' | 'class';
  icon: React.ReactNode;
  difficulty: 'Basic' | 'Intermediate' | 'Advanced';
  topics: string[];
}

const events: EventProps[] = [
  {
    id: '1',
    title: 'Sumall CTF Challenges - 24 Hours Challenge',
    description: 'Intensive 24-hour CTF challenge focusing on Basic Web Exploitation and Networking Exploits. Test your skills in real-world scenarios and compete with fellow cybersecurity enthusiasts.',
    date: '23/07/2025',
    time: '00:00',
    duration: '24 Hours',
    coordinator: {
      name: '<PERSON><PERSON>',
      image: speaker1
    },
    type: 'ctf',
    icon: <Trophy className="h-6 w-6" />,
    difficulty: 'Basic',
    topics: ['Web Exploitation', 'Network Exploits', 'CTF Challenges', 'Penetration Testing']
  },
  {
    id: '2',
    title: 'Server Side Hacking Class',
    description: 'Comprehensive server-side hacking class covering basic techniques, vulnerabilities, and exploitation methods. Learn to identify and exploit server-side vulnerabilities in a controlled environment.',
    date: '25/07/2025',
    time: '10:00',
    duration: '3 Hours',
    coordinator: {
      name: 'K.Arun',
      image: speaker2
    },
    type: 'class',
    icon: <Server className="h-6 w-6" />,
    difficulty: 'Basic',
    topics: ['Server Vulnerabilities', 'SQL Injection', 'Command Injection', 'File Upload Attacks']
  }
];

// Auto-typing animation hook
const useTypewriter = (text: string, speed: number = 100) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, speed]);

  return displayText;
};

// Hacking commands for auto-typing
const hackingCommands = [
  "nmap -sS -O target.com",
  "sqlmap -u 'http://target.com/login.php' --dbs",
  "gobuster dir -u http://target.com -w /usr/share/wordlists/common.txt",
  "nikto -h http://target.com",
  "hydra -l admin -P passwords.txt target.com http-post-form"
];

const TerminalHeader = () => {
  const [currentCommandIndex, setCurrentCommandIndex] = useState(0);
  const [showCursor, setShowCursor] = useState(true);

  const currentCommand = useTypewriter(hackingCommands[currentCommandIndex], 80);

  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  useEffect(() => {
    if (currentCommand === hackingCommands[currentCommandIndex]) {
      const timeout = setTimeout(() => {
        setCurrentCommandIndex(prev => (prev + 1) % hackingCommands.length);
      }, 2000);

      return () => clearTimeout(timeout);
    }
  }, [currentCommand, currentCommandIndex]);

  return (
    <div className="bg-gray-900 border border-psyco-green-muted/30 rounded-none p-3 font-mono text-sm">
      <div className="flex items-center space-x-2">
        <div className="flex space-x-1">
          <div className="w-3 h-3 bg-red-500 rounded-none"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-none"></div>
          <div className="w-3 h-3 bg-green-500 rounded-none"></div>
        </div>
        <div className="text-psyco-green-light flex items-center space-x-1">
          <Terminal className="h-4 w-4" />
          <span>Wolf@root:~</span>
          <span className="text-white">Cyber Wolf --h</span>
        </div>
      </div>
      <div className="mt-2 text-psyco-green-light">
        <span className="text-gray-400">$</span> {currentCommand}
        <span className="ml-1 terminal-cursor">|</span>
      </div>
    </div>
  );
};

const EventCard = ({ event }: { event: EventProps }) => {
  const getTypeColor = (type: string) => {
    return type === 'ctf' ? 'text-yellow-400' : 'text-blue-400';
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Basic': return 'text-green-400';
      case 'Intermediate': return 'text-yellow-400';
      case 'Advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="glassmorphism p-6 card-hover animate-fade-in rounded-none border-2 border-psyco-green-muted/30">
      {/* Terminal-style header for each event */}
      <div className="bg-gray-900 -m-6 mb-4 p-3 font-mono text-xs border-b border-psyco-green-muted/30">
        <div className="flex items-center space-x-2">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-red-500 rounded-none"></div>
            <div className="w-2 h-2 bg-yellow-500 rounded-none"></div>
            <div className="w-2 h-2 bg-green-500 rounded-none"></div>
          </div>
          <span className="text-psyco-green-light">event_{event.id}.exe</span>
        </div>
      </div>

      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={cn(
            "p-2 rounded-none border",
            event.type === 'ctf' ? 'bg-yellow-500/20 border-yellow-500/50' : 'bg-blue-500/20 border-blue-500/50'
          )}>
            <div className={getTypeColor(event.type)}>
              {event.icon}
            </div>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white mb-1">{event.title}</h3>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>{event.date}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>{event.time} ({event.duration})</span>
              </div>
            </div>
          </div>
        </div>
        <div className="text-right">
          <span className={cn(
            "px-2 py-1 rounded-full text-xs font-medium",
            event.type === 'ctf' ? 'bg-yellow-500/20 text-yellow-400' : 'bg-blue-500/20 text-blue-400'
          )}>
            {event.type.toUpperCase()}
          </span>
        </div>
      </div>

      <p className="text-gray-300 mb-4 text-sm leading-relaxed">{event.description}</p>

      <div className="mb-4">
        <h4 className="text-sm font-medium text-white mb-2">Topics Covered:</h4>
        <div className="flex flex-wrap gap-2">
          {event.topics.map((topic, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-psyco-green-muted/20 text-psyco-green-light text-xs rounded-md"
            >
              {topic}
            </span>
          ))}
        </div>
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-none overflow-hidden border-2 border-psyco-green-muted bg-gray-800">
            <img
              src={event.coordinator.image}
              alt={event.coordinator.name}
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <div className="flex items-center space-x-1 text-sm text-gray-400 font-mono">
              <User className="h-3 w-3" />
              <span>root@coordinator:</span>
            </div>
            <p className="text-white font-medium text-sm font-mono">{event.coordinator.name}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs text-gray-400 mb-1 font-mono">./difficulty</div>
          <span className={cn(
            "text-sm font-medium font-mono px-2 py-1 border rounded-none",
            event.difficulty === 'Basic' ? "text-green-400 border-green-400/50 bg-green-400/10" :
            event.difficulty === 'Intermediate' ? "text-yellow-400 border-yellow-400/50 bg-yellow-400/10" :
            "text-red-400 border-red-400/50 bg-red-400/10"
          )}>
            {event.difficulty}
          </span>
        </div>
      </div>

      {/* Terminal command simulation */}
      <div className="mt-4 bg-black p-3 rounded-none border border-gray-700 font-mono text-xs">
        <div className="text-psyco-green-light">
          <span className="text-gray-400">$</span> ./start_event.sh --type={event.type} --date={event.date}
        </div>
        <div className="text-gray-300 mt-1">
          <span className="text-green-400">[READY]</span> Event initialized successfully
        </div>
      </div>
    </div>
  );
};

const UpcomingEvents = () => {
  return (
    <section className="py-16 px-6 md:px-12 bg-gradient-to-b from-psyco-black-DEFAULT to-psyco-black-card">
      <div className="max-w-6xl mx-auto">
        {/* Terminal Header */}
        <div className="mb-8 terminal-glow">
          <TerminalHeader />
          <div className="bg-black border-x border-b border-psyco-green-muted/30 rounded-none p-6 font-mono terminal-scan">
            <div className="text-psyco-green-light">
              <span className="text-gray-400">$</span> ./upcoming_events.sh --list
            </div>
            <div className="mt-2 text-gray-300">
              <span className="text-psyco-green-DEFAULT">[INFO]</span> Loading upcoming cybersecurity events...
            </div>
            <div className="text-gray-300">
              <span className="text-psyco-green-DEFAULT">[SUCCESS]</span> Found {events.length} upcoming events
            </div>
            <div className="mt-2 text-gray-300">
              <span className="text-blue-400">[SCAN]</span> Scanning for vulnerabilities in event database...
            </div>
            <div className="text-gray-300">
              <span className="text-yellow-400">[EXPLOIT]</span> Preparing penetration testing scenarios...
            </div>
            <div className="text-gray-300">
              <span className="text-red-400">[PAYLOAD]</span> Initializing CTF challenge frameworks...
            </div>
          </div>
        </div>

        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Shield className="h-8 w-8 text-psyco-green-DEFAULT" />
            <h2 className="text-4xl font-bold text-white">Upcoming Events</h2>
          </div>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Join our upcoming cybersecurity events, CTF challenges, and training sessions. 
            Enhance your skills with hands-on experience and expert guidance.
          </p>
        </div>

        {/* Events Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {events.map((event, index) => (
            <EventCard
              key={event.id}
              event={event}
            />
          ))}
        </div>

        {/* Call to Action - Terminal Style */}
        <div className="text-center mt-12">
          <div className="glassmorphism p-6 inline-block rounded-none border-2 border-psyco-green-muted/50">
            {/* Terminal header */}
            <div className="bg-gray-900 -m-6 mb-4 p-3 font-mono text-xs border-b border-psyco-green-muted/30">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-red-500 rounded-none"></div>
                  <div className="w-2 h-2 bg-yellow-500 rounded-none"></div>
                  <div className="w-2 h-2 bg-green-500 rounded-none"></div>
                </div>
                <span className="text-psyco-green-light">registration.exe</span>
              </div>
            </div>

            <h3 className="text-xl font-semibold text-white mb-2 font-mono">Ready to Join?</h3>
            <p className="text-gray-400 mb-4 font-mono text-sm">Register now to secure your spot in these exclusive events</p>

            {/* Terminal command style button */}
            <div className="bg-black p-3 rounded-none border border-gray-700 font-mono text-sm mb-4">
              <div className="text-psyco-green-light">
                <span className="text-gray-400">$</span> ./register --events=all --priority=high
              </div>
              <div className="text-gray-300 mt-1">
                <span className="text-yellow-400">[WAITING]</span> Click to execute registration...
              </div>
            </div>

            <Link
              to="/registration"
              className="bg-psyco-green-DEFAULT hover:bg-psyco-green-light text-white px-6 py-2 rounded-none transition-colors duration-300 font-medium font-mono inline-block border border-psyco-green-DEFAULT hover:border-psyco-green-light"
            >
              EXECUTE REGISTRATION
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default UpcomingEvents;
