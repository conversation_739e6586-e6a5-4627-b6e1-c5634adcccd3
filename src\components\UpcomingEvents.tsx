import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, Shield, Terminal, Trophy, Code, Network, Server } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { cn } from '@/lib/utils';
import speaker1 from '@/assets/speaker1.png';
import speaker2 from '@/assets/speaker2.png';

interface EventProps {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  coordinator: {
    name: string;
    image: string;
  };
  type: 'ctf' | 'class';
  icon: React.ReactNode;
  difficulty: 'Basic' | 'Intermediate' | 'Advanced';
  topics: string[];
}

const events: EventProps[] = [
  {
    id: '1',
    title: 'Sumall CTF Challenges - 24 Hours Challenge',
    description: 'Intensive 24-hour CTF challenge focusing on Basic Web Exploitation and Networking Exploits. Test your skills in real-world scenarios and compete with fellow cybersecurity enthusiasts.',
    date: '23/07/2025',
    time: '00:00',
    duration: '24 Hours',
    coordinator: {
      name: '<PERSON><PERSON>',
      image: speaker1
    },
    type: 'ctf',
    icon: <Trophy className="h-6 w-6" />,
    difficulty: 'Basic',
    topics: ['Web Exploitation', 'Network Exploits', 'CTF Challenges', 'Penetration Testing']
  },
  {
    id: '2',
    title: 'Server Side Hacking Class',
    description: 'Comprehensive server-side hacking class covering basic techniques, vulnerabilities, and exploitation methods. Learn to identify and exploit server-side vulnerabilities in a controlled environment.',
    date: '25/07/2025',
    time: '10:00',
    duration: '3 Hours',
    coordinator: {
      name: 'K.Arun',
      image: speaker2
    },
    type: 'class',
    icon: <Server className="h-6 w-6" />,
    difficulty: 'Basic',
    topics: ['Server Vulnerabilities', 'SQL Injection', 'Command Injection', 'File Upload Attacks']
  }
];

const TerminalHeader = () => (
  <div className="bg-gray-900 border border-psyco-green-muted/30 rounded-t-lg p-3 font-mono text-sm">
    <div className="flex items-center space-x-2">
      <div className="flex space-x-1">
        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
      </div>
      <div className="text-psyco-green-light flex items-center space-x-1">
        <Terminal className="h-4 w-4" />
        <span>Wolf@root:~</span>
        <span className="text-white">Cyber Wolf --h</span>
      </div>
    </div>
  </div>
);

const EventCard = ({ event }: { event: EventProps }) => {
  const getTypeColor = (type: string) => {
    return type === 'ctf' ? 'text-yellow-400' : 'text-blue-400';
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Basic': return 'text-green-400';
      case 'Intermediate': return 'text-yellow-400';
      case 'Advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="glassmorphism p-6 card-hover animate-fade-in">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={cn(
            "p-2 rounded-lg",
            event.type === 'ctf' ? 'bg-yellow-500/20' : 'bg-blue-500/20'
          )}>
            <div className={getTypeColor(event.type)}>
              {event.icon}
            </div>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white mb-1">{event.title}</h3>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>{event.date}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>{event.time} ({event.duration})</span>
              </div>
            </div>
          </div>
        </div>
        <div className="text-right">
          <span className={cn(
            "px-2 py-1 rounded-full text-xs font-medium",
            event.type === 'ctf' ? 'bg-yellow-500/20 text-yellow-400' : 'bg-blue-500/20 text-blue-400'
          )}>
            {event.type.toUpperCase()}
          </span>
        </div>
      </div>

      <p className="text-gray-300 mb-4 text-sm leading-relaxed">{event.description}</p>

      <div className="mb-4">
        <h4 className="text-sm font-medium text-white mb-2">Topics Covered:</h4>
        <div className="flex flex-wrap gap-2">
          {event.topics.map((topic, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-psyco-green-muted/20 text-psyco-green-light text-xs rounded-md"
            >
              {topic}
            </span>
          ))}
        </div>
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-psyco-green-muted">
            <img
              src={event.coordinator.image}
              alt={event.coordinator.name}
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <div className="flex items-center space-x-1 text-sm text-gray-400">
              <User className="h-3 w-3" />
              <span>Coordinator:</span>
            </div>
            <p className="text-white font-medium text-sm">{event.coordinator.name}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs text-gray-400 mb-1">Difficulty</div>
          <span className={cn(
            "text-sm font-medium",
            getDifficultyColor(event.difficulty)
          )}>
            {event.difficulty}
          </span>
        </div>
      </div>
    </div>
  );
};

const UpcomingEvents = () => {
  return (
    <section className="py-16 px-6 md:px-12 bg-gradient-to-b from-psyco-black-DEFAULT to-psyco-black-card">
      <div className="max-w-6xl mx-auto">
        {/* Terminal Header */}
        <div className="mb-8">
          <TerminalHeader />
          <div className="bg-black border-x border-b border-psyco-green-muted/30 rounded-b-lg p-6 font-mono">
            <div className="text-psyco-green-light">
              <span className="text-gray-400">$</span> ./upcoming_events.sh --list
            </div>
            <div className="mt-2 text-gray-300">
              <span className="text-psyco-green-DEFAULT">[INFO]</span> Loading upcoming cybersecurity events...
            </div>
            <div className="text-gray-300">
              <span className="text-psyco-green-DEFAULT">[SUCCESS]</span> Found {events.length} upcoming events
            </div>
          </div>
        </div>

        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Shield className="h-8 w-8 text-psyco-green-DEFAULT" />
            <h2 className="text-4xl font-bold text-white">Upcoming Events</h2>
          </div>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Join our upcoming cybersecurity events, CTF challenges, and training sessions. 
            Enhance your skills with hands-on experience and expert guidance.
          </p>
        </div>

        {/* Events Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {events.map((event, index) => (
            <EventCard
              key={event.id}
              event={event}
            />
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <div className="glassmorphism p-6 inline-block">
            <h3 className="text-xl font-semibold text-white mb-2">Ready to Join?</h3>
            <p className="text-gray-400 mb-4">Register now to secure your spot in these exclusive events</p>
            <Link
              to="/registration"
              className="bg-psyco-green-DEFAULT hover:bg-psyco-green-light text-white px-6 py-2 rounded-lg transition-colors duration-300 font-medium inline-block"
            >
              Register Now
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default UpcomingEvents;
