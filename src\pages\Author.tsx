import React, { useEffect, useState, memo } from "react";
import { 
  Shield, 
  Terminal, 
  Wifi, 
  Globe, 
  Search, 
  Lock, 
  Code, 
  Network, 
  Eye, 
  Key,
  ChevronRight,
  Download,
  Github,
  ExternalLink,
  Copy,
  Check,
  Zap,
  Award,
  Users,
  BookOpen
} from "lucide-react";

const Author = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [copiedCommand, setCopiedCommand] = useState<string | null>(null);

  const copyToClipboard = async (text: string, commandType: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCommand(commandType);
      setTimeout(() => setCopiedCommand(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const modules = [
    {
      name: "WiFi Penetration Testing",
      icon: <Wifi className="h-6 w-6" />,
      description: "Advanced wireless security assessment with monitor mode support",
      features: ["Network Discovery", "WEP/WPA/WPA2/WPA3 Analysis", "Evil Twin Detection", "Handshake Capture"],
      command: "wolf.wifi(interface='wlan0', duration=30)",
      cli: "wolf-cli wifi-scan --interface wlan0 --duration 60"
    },
    {
      name: "Subdomain Enumeration",
      icon: <Globe className="h-6 w-6" />,
      description: "High-speed subdomain discovery with custom wordlists and threading",
      features: ["DNS Brute Force", "Threading Architecture", "Zone Transfer Testing", "Subdomain Takeover"],
      command: "wolf.subdomain('example.com', threads=50)",
      cli: "wolf-cli subdomain-enum example.com --threads 50"
    },
    {
      name: "DNS Reconnaissance",
      icon: <Search className="h-6 w-6" />,
      description: "Comprehensive DNS intelligence gathering and analysis",
      features: ["Multi-Record Support", "Reverse DNS", "DNSSEC Validation", "DNS Tunneling Detection"],
      command: "wolf.nslookup('example.com', record_type='ALL')",
      cli: "wolf-cli dns-lookup example.com --type ALL"
    },
    {
      name: "IP Intelligence",
      icon: <Eye className="h-6 w-6" />,
      description: "Deep IP address and domain intelligence gathering",
      features: ["Geolocation Analysis", "ISP Identification", "WHOIS Intelligence", "Reputation Scoring"],
      command: "wolf.ipinfo('*******', include_geolocation=True)",
      cli: "wolf-cli ip-info ******* --geolocation --whois"
    },
    {
      name: "Directory Brute Force",
      icon: <Code className="h-6 w-6" />,
      description: "Smart web application directory discovery with baseline filtering",
      features: ["Multi-Threading", "Smart Filtering", "Recursive Scanning", "Custom Wordlists"],
      command: "wolf.dirbrute('https://example.com', threads=20)",
      cli: "wolf-cli dir-brute https://example.com --threads 20"
    },
    {
      name: "CSRF Testing",
      icon: <Shield className="h-6 w-6" />,
      description: "Advanced Cross-Site Request Forgery vulnerability assessment",
      features: ["Form Analysis", "Token Detection", "State-Changing Operations", "Risk Assessment"],
      command: "wolf.csrf('https://example.com/login')",
      cli: "wolf-cli csrf-test https://example.com/login --advanced"
    },
    {
      name: "Web Application Scanner",
      icon: <Network className="h-6 w-6" />,
      description: "Enterprise-grade web security scanning with SSL/TLS analysis",
      features: ["SSL/TLS Analysis", "Security Headers", "Technology Detection", "Security Scoring"],
      command: "wolf.webscan('https://example.com')",
      cli: "wolf-cli web-scan https://example.com --comprehensive"
    },
    {
      name: "Network Scanner",
      icon: <Terminal className="h-6 w-6" />,
      description: "Comprehensive network security assessment with service detection",
      features: ["Host Discovery", "Port Scanning", "Service Fingerprinting", "OS Detection"],
      command: "wolf.netscan('***********/24', port_range='1-1000')",
      cli: "wolf-cli net-scan ***********/24 --port-range 1-65535"
    },
    {
      name: "Digital Forensics",
      icon: <BookOpen className="h-6 w-6" />,
      description: "Complete forensics analysis with timeline creation and evidence management",
      features: ["File Analysis", "Hash Calculation", "Timeline Creation", "Evidence Chain"],
      command: "wolf.forensics('/path/to/file')",
      cli: "wolf-cli forensics /path/to/evidence --timeline --metadata"
    },
    {
      name: "Cryptographic Analysis",
      icon: <Key className="h-6 w-6" />,
      description: "Hash identification, password cracking, and cipher analysis",
      features: ["Hash Identification", "Dictionary Attacks", "Brute Force", "Entropy Analysis"],
      command: "wolf.crypto('5d41402abc4b2a76b9719d911017c592')",
      cli: "wolf-cli crypto hash --crack --method dictionary"
    }
  ];

  const stats = [
    { label: "Security Modules", value: "10+", icon: <Shield className="h-5 w-5" /> },
    { label: "Years Experience", value: "4+", icon: <Award className="h-5 w-5" /> },
    { label: "Vulnerabilities Found", value: "320+", icon: <Eye className="h-5 w-5" /> },
    { label: "Security Assessments", value: "810+", icon: <Users className="h-5 w-5" /> }
  ];

  const CodeBlock = memo(({ title, code, type }: { title: string; code: string; type: string }) => (
    <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-4 border border-gray-700">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Terminal className="h-4 w-4 text-psyco-green-DEFAULT" />
          <span className="text-psyco-green-DEFAULT text-sm font-semibold">{title}</span>
        </div>
        <button
          onClick={() => copyToClipboard(code, type)}
          className={`flex items-center space-x-1 px-2 py-1 rounded-lg text-xs transition-all duration-200 ${
            copiedCommand === type
              ? 'bg-green-500/20 text-green-400 border border-green-500/50'
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600 border border-gray-600'
          }`}
        >
          {copiedCommand === type ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
          <span>{copiedCommand === type ? 'Copied!' : 'Copy'}</span>
        </button>
      </div>
      <pre className="text-gray-300 text-sm bg-black/30 p-3 rounded-xl border border-gray-800 overflow-x-auto">
        <code className="font-mono">{code}</code>
      </pre>
    </div>
  ));

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT">
      {/* Hero Section */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center space-x-2 bg-psyco-green-DEFAULT/20 text-psyco-green-DEFAULT px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <Zap className="h-4 w-4" />
              <span>Wolf Cybersecurity Toolkit</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Advanced Cybersecurity
              <span className="text-psyco-green-DEFAULT block">Toolkit by S. Tamilselvan</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
              Enterprise-grade cybersecurity and ethical hacking toolkit providing comprehensive security testing 
              capabilities through both programmatic Python APIs and command-line interfaces.
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
              {stats.map((stat, index) => (
                <div key={index} className="glassmorphism p-4 rounded-2xl text-center">
                  <div className="flex items-center justify-center mb-2 text-psyco-green-DEFAULT">
                    {stat.icon}
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-sm text-gray-400">{stat.label}</div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
           <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
  {/* Download Button */}
  <a
    href="https://www.piwheels.org/project/wolf-cybersecurity-toolkit/" // Replace with your actual file link
    download
    className="bg-psyco-green-DEFAULT hover:bg-psyco-green-light text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-psyco-green-DEFAULT/50"
  >
    <Download className="h-5 w-5" />
    <span>Download Wolf Toolkit pypi</span>
  </a>


</div>

          </div>
        </div>
      </section>

      {/* Core Modules Section */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Core Security Modules</h2>
            <p className="text-gray-400 max-w-3xl mx-auto">
              Wolf integrates 10 specialized modules covering all major aspects of cybersecurity assessment,
              from wireless penetration testing to digital forensics.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {modules.map((module, index) => (
              <div key={index} className="glassmorphism p-6 rounded-2xl hover:shadow-lg transition-all duration-300 hover:border-psyco-green-muted/50">
                <div className="flex items-start space-x-4 mb-4">
                  <div className="p-3 bg-psyco-green-DEFAULT/20 rounded-xl text-psyco-green-DEFAULT">
                    {module.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-white mb-2">{module.name}</h3>
                    <p className="text-gray-400 text-sm leading-relaxed">{module.description}</p>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-psyco-green-light mb-3">Key Features:</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {module.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2 text-sm text-gray-300">
                        <ChevronRight className="h-3 w-3 text-psyco-green-DEFAULT" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <CodeBlock title="Python API" code={module.command} type={`python-${index}`} />
                  <CodeBlock title="CLI Command" code={module.cli} type={`cli-${index}`} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Installation & Usage Section */}
      <section className="py-16 px-6 md:px-12 bg-gradient-to-r from-gray-900/50 to-black/50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Installation & Usage</h2>
            <p className="text-gray-400 max-w-3xl mx-auto">
              Get started with Wolf Cybersecurity Toolkit in minutes. Choose between Python API integration
              or command-line interface based on your workflow.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Python Installation */}
            <div className="glassmorphism p-6 rounded-2xl">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <Code className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-bold text-white">Python API</h3>
              </div>

              <div className="space-y-4">
                <CodeBlock
                  title="Installation"
                  code="pip install wolf-cybersecurity-toolkit"
                  type="install-python"
                />
                <CodeBlock
                  title="Basic Usage"
                  code={`import wolf

# WiFi penetration testing
wifi_results = wolf.wifi(interface="wlan0", duration=30)

# Subdomain enumeration
subdomains = wolf.subdomain("example.com", threads=50)

# Web application scanning
web_scan = wolf.webscan("https://example.com")`}
                  type="usage-python"
                />
              </div>
            </div>

            {/* CLI Installation */}
            <div className="glassmorphism p-6 rounded-2xl">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <Terminal className="h-6 w-6 text-green-400" />
                </div>
                <h3 className="text-xl font-bold text-white">Command Line Interface</h3>
              </div>

              <div className="space-y-4">
                <CodeBlock
                  title="Installation"
                  code="curl -sSL https://www.piwheels.org/project/wolf-cybersecurity-toolkit | bash"
                  type="install-cli"
                />
                <CodeBlock
                  title="Basic Usage"
                  code={`# Network scanning
wolf-cli net-scan ***********/24

# Web application testing
wolf-cli web-scan https://example.com

# DNS reconnaissance
wolf-cli dns-lookup example.com --type ALL`}
                  type="usage-cli"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Features Section */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Advanced Capabilities</h2>
            <p className="text-gray-400 max-w-3xl mx-auto">
              Enterprise-grade features designed for professional penetration testers and security researchers.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="glassmorphism p-6 rounded-2xl text-center">
              <div className="p-3 bg-purple-500/20 rounded-xl text-purple-400 mx-auto w-fit mb-4">
                <Zap className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Multi-Threading Architecture</h3>
              <p className="text-gray-400 text-sm">
                Concurrent operations with configurable thread pools and intelligent resource management.
              </p>
            </div>

            <div className="glassmorphism p-6 rounded-2xl text-center">
              <div className="p-3 bg-yellow-500/20 rounded-xl text-yellow-400 mx-auto w-fit mb-4">
                <BookOpen className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Comprehensive Reporting</h3>
              <p className="text-gray-400 text-sm">
                JSON output, evidence databases, timeline analysis, and security scoring.
              </p>
            </div>

            <div className="glassmorphism p-6 rounded-2xl text-center">
              <div className="p-3 bg-red-500/20 rounded-xl text-red-400 mx-auto w-fit mb-4">
                <Shield className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Ethical Safeguards</h3>
              <p className="text-gray-400 text-sm">
                Built-in rate limiting, authorized testing controls, and responsible disclosure guidelines.
              </p>
            </div>
          </div>
        </div>
      </section>

<div className="space-y-4">
     <CodeBlock
                  title="Basic Usage"
                  code={`
"""
Welcome to the Wolf Advanced Security Assessment Framework Sample Code.
"""

import wolf
import json
import time
from datetime import datetime

def advanced_web_security_scan():
    """
    Example: Advanced web application security assessment
    """
    print("=" * 70)
    print("Advanced Web Application Security Assessment")
    print("=" * 70)
    
    target_url = "https://httpbin.org"
    
    try:
        print(f"Starting comprehensive web security scan for {target_url}")
        
        # Comprehensive web application scan
        web_results = wolf.webscan(target_url)
        
        print(f"Security Score: {web_results.get('security_score', 'N/A')}/100")
        
        # SSL/TLS Analysis
        ssl_info = web_results.get('ssl_analysis', {})
        if ssl_info.get('enabled'):
            print(f"SSL/TLS: Enabled - Version {ssl_info.get('version', 'Unknown')}")
        else:
            print("SSL/TLS: Not enabled")
        
        # Security Headers
        headers = web_results.get('security_headers', {})
        missing_headers = len(headers.get('missing_headers', []))
        print(f"Security Headers: {missing_headers} critical headers missing")
        
        # Technology Detection
        tech = web_results.get('technology_detection', {})
        server = tech.get('server', 'Unknown')
        print(f"Server Technology: {server}")
        
        # Vulnerability Summary
        vulnerabilities = (
            len(web_results.get('injection_tests', {}).get('vulnerabilities_found', [])) +
            len(web_results.get('xss_tests', {}).get('vulnerabilities_found', [])) +
            len(web_results.get('file_inclusion_tests', {}).get('vulnerabilities_found', []))
        )
        print(f"Vulnerabilities Found: {vulnerabilities}")
        
    except Exception as e:
        print(f"Web security scan error: {e}")

def network_infrastructure_assessment():
    """
    Example: Network infrastructure security assessment
    """
    print("\n" + "=" * 70)
    print("Network Infrastructure Security Assessment")
    print("=" * 70)
    
    # Using httpbin.org IP for demonstration
    target_network = "*************"
    
    try:
        print(f"Starting network assessment for {target_network}")
        
        # Comprehensive network scan
        net_results = wolf.netscan(target_network, port_range="1-1000", threads=50)
        
        # Host Discovery
        discovery = net_results.get('host_discovery', {})
        live_hosts = len(discovery.get('live_hosts', []))
        print(f"Live Hosts Discovered: {live_hosts}")
        
        # Port Scanning
        port_scan = net_results.get('port_scan', {})
        open_ports = len(port_scan.get('open_ports', []))
        print(f"Open Ports Found: {open_ports}")
        
        # Service Detection
        services = net_results.get('service_detection', {})
        detected_services = len(services.get('services_detected', []))
        print(f"Services Detected: {detected_services}")
        
        # OS Detection
        os_info = net_results.get('os_detection', {})
        os_family = os_info.get('os_family', 'Unknown')
        print(f"Operating System: {os_family}")
        
        # Vulnerability Assessment
        vulns = net_results.get('vulnerability_scan', {})
        vulnerabilities = len(vulns.get('vulnerabilities_found', []))
        print(f"Network Vulnerabilities: {vulnerabilities}")
        
    except Exception as e:
        print(f"Network assessment error: {e}")

def cryptographic_security_analysis():
    """
    Example: Cryptographic analysis and hash cracking
    """
    print("\n" + "=" * 70)
    print("Cryptographic Security Analysis")
    print("=" * 70)
    
    # Example hashes for analysis (common test hashes)
    test_hashes = [
        "5d41402abc4b2a76b9719d911017c592",  # MD5 of "hello"
        "aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d",  # SHA1 of "hello"
        "2cf24dba4f21d4288094c14b6c2c2c2c16c16c16c16c16c16c16c16c16c16c16c16",  # SHA256 example
    ]
    
    for i, test_hash in enumerate(test_hashes, 1):
        try:
            print(f"\nAnalyzing Hash {i}: {test_hash[:20]}...")
            
            crypto_results = wolf.crypto(test_hash, method="dictionary")
            
            # Hash Identification
            hash_id = crypto_results.get('hash_identification', {})
            likely_algos = hash_id.get('likely_algorithms', [])
            print(f"Likely Algorithm: {', '.join(likely_algos) if likely_algos else 'Unknown'}")
            
            # Entropy Analysis
            entropy = crypto_results.get('entropy_analysis', {})
            shannon_entropy = entropy.get('shannon_entropy', 0)
            randomness = entropy.get('randomness_assessment', 'unknown')
            print(f"Entropy: {shannon_entropy:.2f} ({randomness} randomness)")
            
            # Crack Attempts
            crack_results = crypto_results.get('crack_attempts', {})
            found_matches = crack_results.get('found_matches', [])
            if found_matches:
                match = found_matches[0]
                print(f"Hash Cracked: '{match['password']}' using {match['algorithm']}")
            else:
                attempts = crack_results.get('passwords_tested', 0)
                print(f"Hash not cracked ({attempts} passwords tested)")
            
            # Weakness Assessment
            weakness = crypto_results.get('weakness_assessment', {})
            risk_level = weakness.get('risk_level', 'unknown')
            weaknesses = len(weakness.get('identified_weaknesses', []))
            print(f"Risk Level: {risk_level.upper()} ({weaknesses} weaknesses identified)")
            
        except Exception as e:
            print(f"Cryptographic analysis error: {e}")

def digital_forensics_investigation():
    """
    Example: Digital forensics analysis
    """
    print("\n" + "=" * 70)
    print("Digital Forensics Investigation")
    print("=" * 70)
    
    # Create a test file for forensics analysis
    test_file = "test_evidence.txt"
    test_content = "This is test evidence for forensics analysis.\nCreated at: " + datetime.now().isoformat()
    
    try:
        # Create test evidence file
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        print(f"Analyzing evidence file: {test_file}")
        
        # Forensics analysis
        forensics_results = wolf.forensics(test_file)
        
        # File Analysis
        file_analysis = forensics_results.get('file_analysis', {})
        file_size = file_analysis.get('file_size', 0)
        file_type = file_analysis.get('file_type', 'Unknown')
        entropy = file_analysis.get('entropy', 0)
        print(f"File Size: {file_size} bytes")
        print(f"File Type: {file_type}")
        print(f"Entropy: {entropy:.2f}")
        
        # Hash Analysis
        hash_analysis = forensics_results.get('hash_analysis', {})
        md5_hash = hash_analysis.get('md5', 'N/A')
        sha256_hash = hash_analysis.get('sha256', 'N/A')
        print(f"MD5: {md5_hash}")
        print(f"SHA256: {sha256_hash[:20]}...")
        
        # Timestamps
        timestamps = file_analysis.get('timestamps', {})
        created = timestamps.get('created', 'Unknown')
        modified = timestamps.get('modified', 'Unknown')
        print(f"Created: {created}")
        print(f"Modified: {modified}")
        
        # String Analysis
        strings_found = len(file_analysis.get('strings_found', []))
        print(f"Printable Strings: {strings_found} found")
        
        # Evidence Chain
        evidence_chain = forensics_results.get('evidence_chain', [])
        if evidence_chain:
            chain = evidence_chain[0]
            evidence_id = chain.get('evidence_id', 'Unknown')
            investigator = chain.get('investigator', 'Unknown')
            print(f"Evidence ID: {evidence_id}")
            print(f"Investigator: {investigator}")
        
        # Cleanup test file
        import os
        os.remove(test_file)
        
    except Exception as e:
        print(f"Digital forensics error: {e}")

def comprehensive_domain_reconnaissance():
    """
    Example: Complete domain reconnaissance and intelligence gathering
    """
    print("\n" + "=" * 70)
    print("Comprehensive Domain Reconnaissance")
    print("=" * 70)
    
    target_domain = "httpbin.org"
    
    try:
        print(f"Starting comprehensive reconnaissance for {target_domain}")
        
        # DNS Analysis
        print("\n1. DNS Analysis:")
        dns_results = wolf.nslookup(target_domain, record_type="A")
        if not dns_results.get('error'):
            records = len(dns_results.get('records', []))
            print(f"   A Records: {records} found")
            if dns_results.get('records'):
                ip = dns_results['records'][0]['data']
                print(f"   Primary IP: {ip}")
        
        # Subdomain Enumeration
        print("\n2. Subdomain Discovery:")
        subdomains = wolf.subdomain(target_domain, threads=20, timeout=5)
        print(f"   Subdomains Found: {len(subdomains)}")
        for subdomain in subdomains[:5]:
            print(f"   - {subdomain}")
        if len(subdomains) > 5:
            print(f"   ... and {len(subdomains) - 5} more")
        
        # IP Intelligence
        if dns_results.get('records'):
            print("\n3. IP Intelligence:")
            ip_address = dns_results['records'][0]['data']
            ip_info = wolf.ipinfo(ip_address, include_geolocation=True)
            
            geo = ip_info.get('geolocation', {})
            country = geo.get('country', 'Unknown')
            isp = geo.get('isp', 'Unknown')
            print(f"   Location: {country}")
            print(f"   ISP: {isp}")
            
            network_info = ip_info.get('network_info', {})
            open_ports = network_info.get('open_ports', [])
            print(f"   Open Ports: {len(open_ports)} detected")
        
        # Web Security Assessment
        print("\n4. Web Security Check:")
        target_url = f"https://{target_domain}"
        csrf_results = wolf.csrf(target_url)
        
        forms_found = csrf_results.get('forms_found', 0)
        vulnerable_forms = len(csrf_results.get('vulnerable_forms', []))
        risk_level = csrf_results.get('risk_level', 'Unknown')
        print(f"   Forms Analyzed: {forms_found}")
        print(f"   Vulnerable Forms: {vulnerable_forms}")
        print(f"   CSRF Risk Level: {risk_level}")
        
        # Directory Discovery
        print("\n5. Directory Discovery:")
        directories = wolf.dirbrute(target_url, threads=10, extensions=['.php', '.html', '.txt'])
        accessible_paths = len(directories)
        print(f"   Accessible Paths: {accessible_paths}")
        for path_info in directories[:3]:
            path = path_info.get('path', 'Unknown')
            status = path_info.get('status_code', 'Unknown')
            print(f"   - {path} ({status})")
        
    except Exception as e:
        print(f"Domain reconnaissance error: {e}")

def security_assessment_report():
    """
    Generate comprehensive security assessment report
    """
    print("\n" + "=" * 70)
    print("Security Assessment Report Generation")
    print("=" * 70)
    
    target = "httpbin.org"
    
    assessment_report = {
        'target': target,
        'assessment_date': datetime.now().isoformat(),
        'assessment_type': 'Comprehensive Security Assessment',
        'modules_used': [
            'Web Application Scanner',
            'Network Scanner', 
            'DNS Reconnaissance',
            'Subdomain Enumeration',
            'IP Intelligence',
            'Directory Brute Force',
            'CSRF Testing',
            'Cryptographic Analysis',
            'Digital Forensics'
        ],
        'summary': {
            'total_vulnerabilities': 0,
            'risk_score': 0,
            'recommendations': []
        }
    }
    
    try:
        print(f"Generating assessment report for {target}")
        
        # Quick assessment using multiple modules
        dns_info = wolf.nslookup(target, record_type="A")
        subdomains = wolf.subdomain(target, threads=10, timeout=3)
        
        if dns_info.get('records'):
            ip_address = dns_info['records'][0]['data']
            ip_info = wolf.ipinfo(ip_address)
            web_url = f"https://{target}"
            csrf_results = wolf.csrf(web_url)
            
            # Update report with findings
            assessment_report['findings'] = {
                'dns_records': len(dns_info.get('records', [])),
                'subdomains_found': len(subdomains),
                'ip_intelligence': {
                    'country': ip_info.get('geolocation', {}).get('country', 'Unknown'),
                    'isp': ip_info.get('geolocation', {}).get('isp', 'Unknown')
                },
                'web_security': {
                    'forms_analyzed': csrf_results.get('forms_found', 0),
                    'csrf_risk': csrf_results.get('risk_level', 'Unknown')
                }
            }
            
            # Calculate risk score
            vulnerabilities = len(csrf_results.get('vulnerable_forms', []))
            assessment_report['summary']['total_vulnerabilities'] = vulnerabilities
            
            if vulnerabilities == 0:
                assessment_report['summary']['risk_score'] = 85
            elif vulnerabilities <= 2:
                assessment_report['summary']['risk_score'] = 70
            else:
                assessment_report['summary']['risk_score'] = 50
            
            # Generate recommendations
            if vulnerabilities > 0:
                assessment_report['summary']['recommendations'].extend([
                    "Implement CSRF protection on all forms",
                    "Review and strengthen web application security"
                ])
            
            assessment_report['summary']['recommendations'].extend([
                "Regular security assessments",
                "Monitor for new subdomains",
                "Implement security headers",
                "Regular vulnerability scanning"
            ])
        
        # Save report
        report_filename = f"wolf_assessment_{target}_{int(time.time())}.json"
        with open(report_filename, 'w') as f:
            json.dump(assessment_report, f, indent=2)
        
        print(f"\nAssessment Summary:")
        print(f"Target: {assessment_report['target']}")
        print(f"Risk Score: {assessment_report['summary']['risk_score']}/100")
        print(f"Vulnerabilities: {assessment_report['summary']['total_vulnerabilities']}")
        print(f"Recommendations: {len(assessment_report['summary']['recommendations'])}")
        print(f"\nDetailed report saved: {report_filename}")
        
    except Exception as e:
        print(f"Report generation error: {e}")

def main():
    """
    Main function demonstrating all advanced Wolf capabilities
    """
    print(" Wolf Advanced Cybersecurity Toolkit - Comprehensive Assessment")
    print("=" * 70)
    print(" This demonstration uses safe, public targets for testing.")
    print("   Always ensure proper authorization before testing any systems!")
    print()
    
    start_time = time.time()
    
    try:
        # Run comprehensive security assessments
        advanced_web_security_scan()
        network_infrastructure_assessment()
        cryptographic_security_analysis()
        digital_forensics_investigation()
        comprehensive_domain_reconnaissance()
        security_assessment_report()
        
        total_time = time.time() - start_time
        
        print("\n" + "=" * 70)
        print(" Advanced Security Assessment Completed!")
        print(f"  Total Assessment Time: {total_time:.2f} seconds")
        print("All 10 Wolf modules successfully demonstrated")
        print("Comprehensive reports generated")
        print(" Visit: https://github.com/tamilselvan/wolf for documentation")
        
    except KeyboardInterrupt:
        print("\n\nAssessment interrupted by user")
    except Exception as e:
        print(f"\n\n Unexpected error during assessment: {e}")

if __name__ == "__main__":
    main()`} type="usage-cli"/>

</div>

      {/* CyberWolf - Website Vulnerability Scanner Section */}
      <section className="py-16 px-6 md:px-12 bg-gradient-to-r from-red-500/10 to-orange-500/10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center space-x-2 bg-red-500/20 text-red-400 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <Shield className="h-4 w-4" />
              <span>CyberWolf Scanner</span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              CyberWolf - Website Vulnerability Scanner
            </h2>
            <p className="text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Powerful Python-based command-line tool designed for scanning websites for security vulnerabilities.
              Features massive attack vector databases with 100,000+ XSS payloads and 10,000+ SQL injection techniques.
            </p>
          </div>

          {/* Key Features */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div className="glassmorphism p-6 rounded-2xl text-center">
              <div className="p-3 bg-red-500/20 rounded-xl text-red-400 mx-auto w-fit mb-4">
                <Code className="h-8 w-8" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">SQL Injection</h3>
              <p className="text-gray-400 text-sm">10,000+ injection techniques with database-specific attacks</p>
            </div>

            <div className="glassmorphism p-6 rounded-2xl text-center">
              <div className="p-3 bg-orange-500/20 rounded-xl text-orange-400 mx-auto w-fit mb-4">
                <Eye className="h-8 w-8" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">XSS Detection</h3>
              <p className="text-gray-400 text-sm">100,000+ attack vectors with advanced filter bypass</p>
            </div>

            <div className="glassmorphism p-6 rounded-2xl text-center">
              <div className="p-3 bg-yellow-500/20 rounded-xl text-yellow-400 mx-auto w-fit mb-4">
                <Shield className="h-8 w-8" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">CSRF Testing</h3>
              <p className="text-gray-400 text-sm">Cross-site request forgery protection analysis</p>
            </div>

            <div className="glassmorphism p-6 rounded-2xl text-center">
              <div className="p-3 bg-purple-500/20 rounded-xl text-purple-400 mx-auto w-fit mb-4">
                <Search className="h-8 w-8" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Directory Scanning</h3>
              <p className="text-gray-400 text-sm">Hidden path discovery and sensitive file detection</p>
            </div>
          </div>

          {/* Tamil-Brak Attack Mode */}
          <div className="glassmorphism p-8 rounded-2xl mb-12 border-2 border-red-500/30">
            <div className="text-center mb-8">
              <div className="inline-flex items-center space-x-2 bg-red-500/20 text-red-400 px-4 py-2 rounded-full text-sm font-semibold mb-4">
                <Zap className="h-4 w-4" />
                <span>Advanced Attack Mode</span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Tamil-Brak Attack Mode</h3>
              <p className="text-gray-400 max-w-3xl mx-auto">
                Comprehensive multi-phase attack developed by S.Tamilselvan for advanced vulnerability detection
                that goes beyond traditional scanning methods.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gray-800/50 p-4 rounded-xl">
                <h4 className="text-white font-semibold mb-2">Server Information Gathering</h4>
                <p className="text-gray-400 text-sm">Technology stack identification and security misconfiguration detection</p>
              </div>
              <div className="bg-gray-800/50 p-4 rounded-xl">
                <h4 className="text-white font-semibold mb-2">Advanced Bug Detection</h4>
                <p className="text-gray-400 text-sm">File inclusion, deserialization, and business logic flaw detection</p>
              </div>
              <div className="bg-gray-800/50 p-4 rounded-xl">
                <h4 className="text-white font-semibold mb-2">Path Traversal Testing</h4>
                <p className="text-gray-400 text-sm">Sensitive path discovery and directory traversal vulnerability testing</p>
              </div>
              <div className="bg-gray-800/50 p-4 rounded-xl">
                <h4 className="text-white font-semibold mb-2">Infrastructure Analysis</h4>
                <p className="text-gray-400 text-sm">Security header analysis and infrastructure misconfiguration detection</p>
              </div>
              <div className="bg-gray-800/50 p-4 rounded-xl">
                <h4 className="text-white font-semibold mb-2">Concurrent Scanning</h4>
                <p className="text-gray-400 text-sm">Multi-threaded scanning with intelligent rate limiting</p>
              </div>
              <div className="bg-gray-800/50 p-4 rounded-xl">
                <h4 className="text-white font-semibold mb-2">Comprehensive Reports</h4>
                <p className="text-gray-400 text-sm">Detailed vulnerability reports with remediation advice</p>
              </div>
            </div>
          </div>

          {/* Usage Examples */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* CLI Usage */}
            <div className="glassmorphism p-6 rounded-2xl">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <Terminal className="h-6 w-6 text-green-400" />
                </div>
                <h3 className="text-xl font-bold text-white">CyberWolf CLI</h3>
              </div>

              <div className="space-y-4">
                <CodeBlock
                  title="Basic Scan"
                  code="python cyberwolf.py scan https://example.com"
                  type="cyberwolf-basic"
                />
                <CodeBlock
                  title="Tamil-Brak Attack Mode"
                  code="python cyberwolf.py scan --tamil-brak https://example.com"
                  type="cyberwolf-tamil-brak"
                />
                <CodeBlock
                  title="Advanced Options"
                  code={`python cyberwolf.py scan \\
  --output html \\
  --output-file report.html \\
  --threads 10 \\
  --scan-depth 5 \\
  https://example.com`}
                  type="cyberwolf-advanced"
                />
              </div>
            </div>

            {/* CyberWolf Installation */}
            <div className="glassmorphism p-6 rounded-2xl">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <Download className="h-6 w-6 text-purple-400" />
                </div>
                <h3 className="text-xl font-bold text-white">Installation</h3>
              </div>

              <div className="space-y-4">
                <CodeBlock
                  title="Clone Repository"
                  code="git clone https://github.com/Tamilselvan-S-Cyber-Security/Cyber_wolf_Vulnerability_Scanner.git"
                  type="cyberwolf-clone"
                />
                <CodeBlock
                  title="Setup Dependencies"
                  code={`cd Cyber_wolf_Vulnerability_Scanner
pip install -r requirements.txt`}
                  type="cyberwolf-setup"
                />
                <CodeBlock
                  title="Run Scanner"
                  code="python cyberwolf.py scan https://example.com"
                  type="cyberwolf-run"
                />
              </div>
            </div>
          </div>

          {/* Attack Vector Databases Section */}
          <div className="mt-12 glassmorphism p-8 rounded-2xl">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <div className="p-2 bg-red-500/20 rounded-lg">
                  <Eye className="h-6 w-6 text-red-400" />
                </div>
                <h3 className="text-2xl font-bold text-white">Attack Vector Databases</h3>
              </div>
              <p className="text-gray-400 max-w-3xl mx-auto">
                CyberWolf includes comprehensive attack vector databases for detecting even the most obscure vulnerabilities.
              </p>
            </div>

              <div className="space-y-4">
                <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 p-4 rounded-xl border border-orange-500/30">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-semibold">XSS Payloads</h4>
                    <span className="text-2xl font-bold text-orange-400">100,000+</span>
                  </div>
                  <p className="text-gray-400 text-sm">Advanced HTML5 vectors, DOM-based payloads, filter bypasses</p>
                </div>

                <div className="bg-gradient-to-r from-red-500/10 to-purple-500/10 p-4 rounded-xl border border-red-500/30">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-semibold">SQL Injections</h4>
                    <span className="text-2xl font-bold text-red-400">10,000+</span>
                  </div>
                  <p className="text-gray-400 text-sm">Boolean-based, time-based, UNION attacks, database-specific</p>
                </div>

                <div className="bg-gray-800/50 p-4 rounded-xl">
                  <h4 className="text-white font-semibold mb-2">Web Interface</h4>
                  <p className="text-gray-400 text-sm">Real-time progress tracking with interactive vulnerability reports</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CyberWolf Example Section */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-6xl mx-auto">
          {/* Image section */}
          <div className="w-full max-w-xl mx-auto p-4 mb-8">
            <img
              src="/images/Toolimage.png"
              alt="Cyber Wolf"
              className="w-full h-auto object-cover rounded-xl shadow-lg"
            />
          </div>

          <div className="space-y-4">
     <CodeBlock
                  title="Basic Usage"
                  code={`

"""
CyberWolf Example Scan Script

This script demonstrates how to use CyberWolf for website security scanning
with a focus on the Tamil-Brak attack mode.
"""

import os
import sys
import time
from urllib.parse import urlparse

print("""
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     
                                                         EXAMPLE SCAN SCRIPT
""")

print("\nCYBERWOLF SECURITY SCANNER - DEMONSTRATION")
print("=" * 50)
print("\nThis example will demonstrate how to run a security scan using CyberWolf.")
print("You can modify this script to scan your own targets.\n")

# Default target (can be changed by user)
target_url = "https://example.com"

# Ask user if they want to scan a different URL
use_custom = input("Do you want to scan a custom URL? (y/n) [default: n]: ").lower() == 'y'

if use_custom:
    custom_url = input("Enter the URL to scan (include http:// or https://): ")
    if custom_url:
        target_url = custom_url

# Validate URL format
parsed_url = urlparse(target_url)
if not all([parsed_url.scheme, parsed_url.netloc]):
    print("\n[ERROR] Invalid URL format. Please include http:// or https://")
    sys.exit(1)

print(f"\nTarget URL: {target_url}")

# Select scan type
print("\nSCAN OPTIONS:")
print("1. Basic scan (SQL injection, XSS, CSRF, directory scanning)")
print("2. Full scan with Tamil-Brak attack mode (comprehensive security test)")
scan_choice = input("Select scan type (1/2) [default: 1]: ") or "1"

# Configure output format
print("\nOUTPUT OPTIONS:")
print("1. Console output (results displayed in terminal)")
print("2. HTML report (saved as HTML file)")
print("3. PDF report (saved as PDF file)")
output_choice = input("Select output format (1/2/3) [default: 1]: ") or "1"

output_format = {
    "1": "console", 
    "2": "html",
    "3": "pdf"
}.get(output_choice, "console")

# Set up output file if needed
output_file = None
if output_format in ["html", "pdf"]:
    domain = parsed_url.netloc.replace(':', '_')
    file_ext = "html" if output_format == "html" else "pdf"
    default_filename = f"cyberwolf_report_{domain}_{int(time.time())}.{file_ext}"
    
    output_file = input(f"Enter output filename [default: {default_filename}]: ") or default_filename
    print(f"\nReport will be saved as: {output_file}")

# Build the command
command = f"python cyberwolf.py scan {target_url} --output {output_format}"

if output_file:
    command += f" --output-file {output_file}"

# Add Tamil-Brak attack mode if selected
if scan_choice == "2":
    command += " --tamil-brak"

# Show the command that will be run
print("\n" + "=" * 50)
print(f"COMMAND TO EXECUTE: {command}")
print("=" * 50)

# Ask for confirmation before running
print("\nDISCLAIMER: Only scan websites that you have permission to test.")
print("Unauthorized scanning of websites may be illegal and unethical.")
proceed = input("\nProceed with scan? (y/n) [default: n]: ").lower() == 'y'

if proceed:
    print("\nStarting scan, please wait...\n")
    os.system(command)
    
    if output_format in ["html", "pdf"]:
        print(f"\nScan complete! Report saved to {output_file}")
    else:
        print("\nScan complete!")
else:
    print("\nScan cancelled.")

`} type="usage-cli"/>

          </div>
        </div>
      </section>

      {/* Author Section */}
      <section className="py-16 px-6 md:px-12 bg-gradient-to-r from-psyco-green-DEFAULT/10 to-blue-500/10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="glassmorphism p-8 rounded-2xl">
            <div className="w-24 h-24 bg-gradient-to-r from-psyco-green-DEFAULT to-blue-500 rounded-full mx-auto mb-6 flex items-center justify-center">
              <span className="text-2xl font-bold text-white">ST</span>
            </div>
            <h2 className="text-2xl font-bold text-white mb-4">S. Tamilselvan</h2>
            <p className="text-psyco-green-DEFAULT font-semibold mb-4">
              Lead Cybersecurity Architect & Ethical Hacking Specialist
            </p>
            <p className="text-gray-400 leading-relaxed mb-6">
              With over 15 years of experience in cybersecurity, S. Tamilselvan has developed comprehensive
              security tools including Wolf Toolkit and CyberWolf Scanner for professional penetration testers
              and security researchers. His expertise spans network security, web application testing, digital forensics, and cryptographic analysis.
            </p>
            <div className="flex items-center justify-center space-x-4">
              <button className="flex items-center space-x-2 bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                <Github className="h-4 w-4" />
                <span>GitHub</span>
              </button>
              <button className="flex items-center space-x-2 bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                <ExternalLink className="h-4 w-4" />
                <span>Portfolio</span>
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Author;
