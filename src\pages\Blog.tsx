
import React, { useEffect, useState } from "react";
import BlogPost from "@/components/BlogPost";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

const Blog = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  
  const [searchTerm, setSearchTerm] = useState("");
  
  const blogPosts = [
    {
      id: "security-research-framework",
      title: "Advanced Security Research Framework: Comprehensive Reconnaissance Guide",
      excerpt: "Complete framework for advanced passive reconnaissance and intelligence gathering with Python automation tools and OSINT integration.",
      date: "January 10, 2024",
      readTime: "18 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://www.engineersgarage.com/wp-content/uploads/2019/07/Representational-Image-For-Computer-Hacking.jpg",
      featured: true
    },
    {
      id: "threat-intelligence-matrix",
      title: "Comprehensive Vulnerability Classification & Threat Intelligence Matrix",
      excerpt: "Advanced threat intelligence framework with CVSS scoring, exploitability analysis, and comprehensive attack vector classification system.",
      date: "January 8, 2024",
      readTime: "15 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTaItztIZUkqPQ8wBbZEaldJErr_nuzS3Mcuw&s"
    },
    {
      id: "advanced-osint-techniques",
      title: "Advanced OSINT Techniques for Security Researchers",
      excerpt: "Professional Open Source Intelligence gathering methods, tools, and automation techniques for comprehensive target analysis.",
      date: "January 5, 2024",
      readTime: "14 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTRtttgK86ASbgLSSFKnwyDsigtCnDoQtZS7w&s"
    },
    {
      id: "web-penetration-testing-fundamentals",
      title: "Web Penetration Testing Fundamentals: A Complete Guide",
      excerpt: "Comprehensive guide to web application penetration testing methodologies, tools, and best practices for identifying security vulnerabilities.",
      date: "December 15, 2023",
      readTime: "12 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://eluminoustechnologies.com/blog/wp-content/uploads/2024/01/1-2.png",
      featured: false
    },
    {
      id: "owasp-top-10-2023",
      title: "OWASP Top 10 2023: Critical Web Application Security Risks",
      excerpt: "Deep dive into the latest OWASP Top 10 vulnerabilities and practical mitigation strategies for web application security.",
      date: "November 28, 2023",
      readTime: "10 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://www.reflectiz.com/wp-content/uploads/2023/08/Blog-page-images-79.jpg"
    },
    {
      id: "cybersecurity-frameworks-compliance",
      title: "Cybersecurity Frameworks and Compliance Standards",
      excerpt: "Understanding NIST, ISO 27001, SOC 2, and other essential cybersecurity frameworks for enterprise security programs.",
      date: "October 20, 2023",
      readTime: "8 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQC3YED5f-lzhBPRace936gYRNgjjUGEhb6Kw&s"
    },
    {
      id: "vulnerability-assessment-methodology",
      title: "Vulnerability Assessment Methodology and Best Practices",
      excerpt: "Step-by-step guide to conducting effective vulnerability assessments using industry-standard tools and methodologies.",
      date: "September 12, 2023",
      readTime: "9 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTkgGMq9xBsPx_AnhyeqZgqOq-yrFCXmVPHCg&s"
    },
    {
      id: "security-auditing-best-practices",
      title: "Security Auditing Best Practices for Enterprise Systems",
      excerpt: "Comprehensive guide to conducting security audits, including compliance requirements, audit methodologies, and reporting standards.",
      date: "August 5, 2023",
      readTime: "11 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "data:image/jpeg;base64,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"
    },
    {
      id: "incident-response-playbook",
      title: "Incident Response Playbook: From Detection to Recovery",
      excerpt: "Essential incident response procedures, team coordination strategies, and recovery methodologies for cybersecurity incidents.",
      date: "July 18, 2023",
      readTime: "13 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://www.magnetforensics.com/wp-content/uploads/2023/02/MF_EnhancingIncidentResponsePlaybook_FrameworkDiagram1600x657.png"
    }
  ];
  
  const categories = [
    "All",
    "Cybersecurity"
  ];
  
  const [activeCategory, setActiveCategory] = useState("All");
  
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesCategory = activeCategory === "All" || post.category === activeCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="bg-psyco-black-light py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in">Blog & Insights</h1>
            <p className="text-xl text-gray-300 mb-8 animate-fade-in animation-delay-100">
              Industry knowledge, technical tips, and event inspiration from our expert team
            </p>
          </div>
          
          <div className="flex flex-col md:flex-row gap-4 items-center animate-fade-in animation-delay-200">
            <div className="relative w-full md:w-1/2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-psyco-black-DEFAULT border-psyco-green-muted/50 w-full"
              />
            </div>
            
            <div className="w-full md:w-1/2 flex gap-2 overflow-x-auto pb-2 no-scrollbar flex-nowrap md:justify-end">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                    activeCategory === category
                      ? "bg-psyco-green-DEFAULT text-white"
                      : "bg-psyco-black-DEFAULT text-gray-300 hover:bg-psyco-black-card"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* Blog Posts */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          {filteredPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <BlogPost
                  key={post.id}
                  {...post}
                  className={`animate-fade-in ${post.featured ? "md:col-span-2" : ""}`}
                  style={{ animationDelay: `${index * 100}ms` }}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <h3 className="text-xl text-white mb-2">No posts found</h3>
              <p className="text-gray-400">Try adjusting your search or category filter</p>
            </div>
          )}
        </div>
      </section>
      
      {/* Newsletter Section */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="glassmorphism p-8 md:p-12 text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Stay Updated with Industry Insights</h2>
            <p className="text-gray-300 max-w-2xl mx-auto mb-8">
              Subscribe to our newsletter to receive the latest articles, tips, and industry news directly in your inbox.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
              <Input
                type="email"
                placeholder="Your email address"
                className="bg-psyco-black-DEFAULT border-psyco-green-muted/50 flex-grow"
              />
              <button className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-2 px-6 rounded-lg transition-colors">
                Subscribe
              </button>
            </div>
            
            <p className="text-gray-400 text-sm mt-4">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Blog;
