import React, { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Home, ArrowLeft, Search, Shield, AlertTriangle, ExternalLink, BookOpen, Users } from "lucide-react";

const NotFound404 = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const quickLinks = [
    {
      title: "Home",
      description: "Return to our homepage",
      icon: <Home className="h-5 w-5" />,
      path: "/",
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "Cybersecurity Courses",
      description: "Explore our training programs",
      icon: <Shield className="h-5 w-5" />,
      path: "/services",
      color: "from-psyco-green-DEFAULT to-psyco-green-dark"
    },
    {
      title: "Course Roadmap",
      description: "View our complete curriculum",
      icon: <BookOpen className="h-5 w-5" />,
      path: "/roadmap",
      color: "from-purple-500 to-purple-600"
    },
    {
      title: "Blog & Resources",
      description: "Read cybersecurity articles",
      icon: <Search className="h-5 w-5" />,
      path: "/blog",
      color: "from-orange-500 to-orange-600"
    }
  ];

  const helpfulResources = [
    {
      title: "Free Training Registration",
      description: "Join our monthly cybersecurity training",
      url: "https://cyberwolf-career-guidance.web.app/registration.html",
      icon: <Users className="h-4 w-4" />
    },
    {
      title: "Course Details",
      description: "Learn more about our programs",
      url: "https://cyberwolf-career-guidance.web.app/details.html",
      icon: <BookOpen className="h-4 w-4" />
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl w-full text-center">
        
        {/* 404 Animation */}
        <div className="relative mb-8">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-64 h-64 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl animate-pulse"></div>
          </div>
          <div className="relative z-10">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-red-500/20 rounded-full mb-6">
              <AlertTriangle className="h-12 w-12 text-red-400 animate-bounce" />
            </div>
            <h1 className="text-8xl sm:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-psyco-green-DEFAULT to-psyco-green-dark mb-4 animate-fade-in">
              404
            </h1>
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-12 animate-fade-in animation-delay-200">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Oops! Page Not Found
          </h2>
          <p className="text-lg text-gray-300 mb-2 max-w-2xl mx-auto">
            The page you're looking for seems to have vanished into the cyber void. 
            Don't worry, even the best penetration testers sometimes take a wrong turn!
          </p>
          <p className="text-gray-400 max-w-xl mx-auto">
            Let's get you back on track to mastering cybersecurity with Cyber Wolf.
          </p>
        </div>

        {/* Quick Navigation */}
        <div className="mb-12 animate-fade-in animation-delay-400">
          <h3 className="text-xl font-semibold text-white mb-6">Quick Navigation</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickLinks.map((link, index) => (
              <Link
                key={index}
                to={link.path}
                className="group glassmorphism p-6 rounded-xl hover:transform hover:scale-105 transition-all duration-300 text-left"
              >
                <div className={`w-12 h-12 bg-gradient-to-r ${link.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <div className="text-white">
                    {link.icon}
                  </div>
                </div>
                <h4 className="text-white font-semibold mb-2 group-hover:text-psyco-green-DEFAULT transition-colors">
                  {link.title}
                </h4>
                <p className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors">
                  {link.description}
                </p>
              </Link>
            ))}
          </div>
        </div>

        {/* Helpful Resources */}
        <div className="mb-12 animate-fade-in animation-delay-600">
          <h3 className="text-xl font-semibold text-white mb-6">Helpful Resources</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-2xl mx-auto">
            {helpfulResources.map((resource, index) => (
              <a
                key={index}
                href={resource.url}
                target="_blank"
                rel="noopener noreferrer"
                className="group glassmorphism p-4 rounded-lg hover:transform hover:scale-105 transition-all duration-300 flex items-center space-x-4"
              >
                <div className="w-10 h-10 bg-psyco-green-DEFAULT rounded-lg flex items-center justify-center group-hover:bg-psyco-green-dark transition-colors">
                  <div className="text-white">
                    {resource.icon}
                  </div>
                </div>
                <div className="flex-1 text-left">
                  <h4 className="text-white font-medium group-hover:text-psyco-green-DEFAULT transition-colors">
                    {resource.title}
                  </h4>
                  <p className="text-gray-400 text-sm">
                    {resource.description}
                  </p>
                </div>
                <ExternalLink className="h-4 w-4 text-gray-400 group-hover:text-psyco-green-DEFAULT transition-colors" />
              </a>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in animation-delay-800">
          <Link
            to="/"
            className="inline-flex items-center bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-3 px-8 rounded-lg transition-all duration-300 btn-glow"
          >
            <Home className="mr-2 h-5 w-5" />
            Go Home
          </Link>
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center bg-transparent border border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10 font-medium py-3 px-8 rounded-lg transition-all duration-300"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Go Back
          </button>
        </div>

        {/* Fun Cyber Wolf Message */}
        <div className="mt-16 glassmorphism p-6 rounded-xl max-w-2xl mx-auto animate-fade-in animation-delay-1000">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
              <span className="text-white text-xl">🐺</span>
            </div>
          </div>
          <h4 className="text-white font-semibold mb-2">Cyber Wolf Tip</h4>
          <p className="text-gray-300 text-sm">
            "In cybersecurity, getting lost is part of the learning process. Every wrong turn teaches us something new about the digital landscape. 
            Keep exploring, keep learning, and remember - the best hackers are those who never stop being curious!"
          </p>
        </div>

        {/* Footer Note */}
        <div className="mt-12 text-center">
          <p className="text-gray-500 text-sm">
            Error Code: 404 | Page Not Found | 
            <span className="text-psyco-green-DEFAULT ml-1">Cyber Wolf Security Training</span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound404;
